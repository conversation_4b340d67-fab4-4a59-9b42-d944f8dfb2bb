package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 风格迁移响应DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "风格迁移响应")
public class StyleTransferResponse {

    @Schema(description = "操作是否成功", example = "true")
    @JsonProperty("success")
    private boolean success;

    @Schema(description = "响应消息", example = "风格迁移完成")
    @JsonProperty("message")
    private String message;

    @Schema(description = "迁移任务ID", example = "transfer_123456")
    @JsonProperty("transferId")
    private String transferId;

    @Schema(description = "原始图片路径")
    @JsonProperty("originalImagePath")
    private String originalImagePath;

    @Schema(description = "风格化图片路径")
    @JsonProperty("styledImagePath")
    private String styledImagePath;

    @Schema(description = "风格化图片URL")
    @JsonProperty("styledImageUrl")
    private String styledImageUrl;

    @Schema(description = "缩略图URL")
    @JsonProperty("thumbnailUrl")
    private String thumbnailUrl;

    @Schema(description = "预览图URL")
    @JsonProperty("previewUrl")
    private String previewUrl;

    @Schema(description = "使用的风格类型")
    @JsonProperty("styleType")
    private String styleType;

    @Schema(description = "风格名称")
    @JsonProperty("styleName")
    private String styleName;

    @Schema(description = "处理状态", example = "completed")
    @JsonProperty("status")
    private String status;

    @Schema(description = "处理进度", example = "100")
    @JsonProperty("progress")
    private int progress;

    @Schema(description = "处理时间(毫秒)", example = "5000")
    @JsonProperty("processingTime")
    private long processingTime;

    @Schema(description = "创建时间")
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    @Schema(description = "完成时间")
    @JsonProperty("completedAt")
    private LocalDateTime completedAt;

    @Schema(description = "文件大小(字节)", example = "1048576")
    @JsonProperty("fileSize")
    private long fileSize;

    @Schema(description = "图片尺寸", example = "1024x768")
    @JsonProperty("dimensions")
    private String dimensions;

    @Schema(description = "质量评分", example = "0.95")
    @JsonProperty("qualityScore")
    private float qualityScore;

    @Schema(description = "风格相似度", example = "0.88")
    @JsonProperty("styleSimilarity")
    private float styleSimilarity;

    @Schema(description = "处理参数")
    @JsonProperty("parameters")
    private Map<String, Object> parameters;

    @Schema(description = "错误信息")
    @JsonProperty("errorMessage")
    private String errorMessage;

    @Schema(description = "错误代码")
    @JsonProperty("errorCode")
    private String errorCode;

    @Schema(description = "统计信息")
    @JsonProperty("statistics")
    private TransferStatistics statistics;

    @Schema(description = "元数据")
    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    // 构造函数
    public StyleTransferResponse() {
        this.createdAt = LocalDateTime.now();
        this.status = "pending";
        this.progress = 0;
    }

    // 静态工厂方法
    public static StyleTransferResponse success(String transferId, String message) {
        StyleTransferResponse response = new StyleTransferResponse();
        response.setSuccess(true);
        response.setTransferId(transferId);
        response.setMessage(message);
        response.setStatus("completed");
        response.setProgress(100);
        response.setCompletedAt(LocalDateTime.now());
        return response;
    }

    public static StyleTransferResponse error(String message) {
        StyleTransferResponse response = new StyleTransferResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setStatus("failed");
        response.setErrorMessage(message);
        return response;
    }

    public static StyleTransferResponse processing(String transferId, int progress) {
        StyleTransferResponse response = new StyleTransferResponse();
        response.setSuccess(true);
        response.setTransferId(transferId);
        response.setMessage("处理中...");
        response.setStatus("processing");
        response.setProgress(progress);
        return response;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTransferId() {
        return transferId;
    }

    public void setTransferId(String transferId) {
        this.transferId = transferId;
    }

    public String getOriginalImagePath() {
        return originalImagePath;
    }

    public void setOriginalImagePath(String originalImagePath) {
        this.originalImagePath = originalImagePath;
    }

    public String getStyledImagePath() {
        return styledImagePath;
    }

    public void setStyledImagePath(String styledImagePath) {
        this.styledImagePath = styledImagePath;
    }

    public String getStyledImageUrl() {
        return styledImageUrl;
    }

    public void setStyledImageUrl(String styledImageUrl) {
        this.styledImageUrl = styledImageUrl;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getStyleType() {
        return styleType;
    }

    public void setStyleType(String styleType) {
        this.styleType = styleType;
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(long processingTime) {
        this.processingTime = processingTime;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getDimensions() {
        return dimensions;
    }

    public void setDimensions(String dimensions) {
        this.dimensions = dimensions;
    }

    public float getQualityScore() {
        return qualityScore;
    }

    public void setQualityScore(float qualityScore) {
        this.qualityScore = qualityScore;
    }

    public float getStyleSimilarity() {
        return styleSimilarity;
    }

    public void setStyleSimilarity(float styleSimilarity) {
        this.styleSimilarity = styleSimilarity;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public TransferStatistics getStatistics() {
        return statistics;
    }

    public void setStatistics(TransferStatistics statistics) {
        this.statistics = statistics;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    /**
     * 风格信息内部类
     */
    @Schema(description = "风格信息")
    public static class StyleInfo {
        @Schema(description = "风格ID")
        private String styleId;

        @Schema(description = "风格名称")
        private String styleName;

        @Schema(description = "风格类型")
        private String styleType;

        @Schema(description = "风格描述")
        private String description;

        @Schema(description = "预览图URL")
        private String previewUrl;

        @Schema(description = "风格标签")
        private List<String> tags;

        @Schema(description = "艺术家名称")
        private String artist;

        @Schema(description = "创作年代")
        private String period;

        @Schema(description = "推荐度", example = "0.9")
        private float recommendationScore;

        @Schema(description = "使用次数", example = "150")
        private int usageCount;

        @Schema(description = "平均评分", example = "4.5")
        private float averageRating;

        @Schema(description = "是否为自定义风格", example = "false")
        private boolean isCustom;

        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        // Getters and Setters
        public String getStyleId() { return styleId; }
        public void setStyleId(String styleId) { this.styleId = styleId; }

        public String getStyleName() { return styleName; }
        public void setStyleName(String styleName) { this.styleName = styleName; }

        public String getStyleType() { return styleType; }
        public void setStyleType(String styleType) { this.styleType = styleType; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getPreviewUrl() { return previewUrl; }
        public void setPreviewUrl(String previewUrl) { this.previewUrl = previewUrl; }

        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }

        public String getArtist() { return artist; }
        public void setArtist(String artist) { this.artist = artist; }

        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }

        public float getRecommendationScore() { return recommendationScore; }
        public void setRecommendationScore(float recommendationScore) { this.recommendationScore = recommendationScore; }

        public int getUsageCount() { return usageCount; }
        public void setUsageCount(int usageCount) { this.usageCount = usageCount; }

        public float getAverageRating() { return averageRating; }
        public void setAverageRating(float averageRating) { this.averageRating = averageRating; }

        public boolean isCustom() { return isCustom; }
        public void setCustom(boolean custom) { isCustom = custom; }

        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    }

    /**
     * 迁移统计信息内部类
     */
    @Schema(description = "迁移统计信息")
    public static class TransferStatistics {
        @Schema(description = "总迁移次数", example = "1250")
        private int totalTransfers;

        @Schema(description = "成功次数", example = "1200")
        private int successfulTransfers;

        @Schema(description = "失败次数", example = "50")
        private int failedTransfers;

        @Schema(description = "平均处理时间(毫秒)", example = "3500")
        private long averageProcessingTime;

        @Schema(description = "最受欢迎的风格")
        private String mostPopularStyle;

        @Schema(description = "今日迁移次数", example = "25")
        private int todayTransfers;

        @Schema(description = "本周迁移次数", example = "180")
        private int weeklyTransfers;

        @Schema(description = "本月迁移次数", example = "750")
        private int monthlyTransfers;

        @Schema(description = "风格使用分布")
        private Map<String, Integer> styleDistribution;

        @Schema(description = "质量分布")
        private Map<String, Integer> qualityDistribution;

        @Schema(description = "处理时间分布")
        private Map<String, Integer> timeDistribution;

        // Getters and Setters
        public int getTotalTransfers() { return totalTransfers; }
        public void setTotalTransfers(int totalTransfers) { this.totalTransfers = totalTransfers; }

        public int getSuccessfulTransfers() { return successfulTransfers; }
        public void setSuccessfulTransfers(int successfulTransfers) { this.successfulTransfers = successfulTransfers; }

        public int getFailedTransfers() { return failedTransfers; }
        public void setFailedTransfers(int failedTransfers) { this.failedTransfers = failedTransfers; }

        public long getAverageProcessingTime() { return averageProcessingTime; }
        public void setAverageProcessingTime(long averageProcessingTime) { this.averageProcessingTime = averageProcessingTime; }

        public String getMostPopularStyle() { return mostPopularStyle; }
        public void setMostPopularStyle(String mostPopularStyle) { this.mostPopularStyle = mostPopularStyle; }

        public int getTodayTransfers() { return todayTransfers; }
        public void setTodayTransfers(int todayTransfers) { this.todayTransfers = todayTransfers; }

        public int getWeeklyTransfers() { return weeklyTransfers; }
        public void setWeeklyTransfers(int weeklyTransfers) { this.weeklyTransfers = weeklyTransfers; }

        public int getMonthlyTransfers() { return monthlyTransfers; }
        public void setMonthlyTransfers(int monthlyTransfers) { this.monthlyTransfers = monthlyTransfers; }

        public Map<String, Integer> getStyleDistribution() { return styleDistribution; }
        public void setStyleDistribution(Map<String, Integer> styleDistribution) { this.styleDistribution = styleDistribution; }

        public Map<String, Integer> getQualityDistribution() { return qualityDistribution; }
        public void setQualityDistribution(Map<String, Integer> qualityDistribution) { this.qualityDistribution = qualityDistribution; }

        public Map<String, Integer> getTimeDistribution() { return timeDistribution; }
        public void setTimeDistribution(Map<String, Integer> timeDistribution) { this.timeDistribution = timeDistribution; }
    }
}
