package com.example.photoupload.service;

import com.example.photoupload.dto.FaceDetectionResult;

import java.io.InputStream;
import java.util.List;

/**
 * 人脸识别服务接口
 */
public interface FaceRecognitionService {
    
    /**
     * 检测图片中的人脸
     * @param imageStream 图片输入流
     * @param fileName 文件名（用于记录）
     * @return 人脸检测结果
     */
    FaceDetectionResult detectFaces(InputStream imageStream, String fileName);
    
    /**
     * 识别人脸并分组
     * @param imageStream 图片输入流
     * @param fileName 文件名
     * @return 分组后的人脸信息
     */
    List<FaceDetectionResult.Face> recognizeAndGroupFaces(InputStream imageStream, String fileName);
    
    /**
     * 识别人脸并匹配已知人物
     * @param imageStream 图片输入流
     * @param knownFaces 已知人脸库
     * @return 匹配结果
     */
    String matchKnownFace(InputStream imageStream, List<FaceDetectionResult.Face> knownFaces);
    
    /**
     * 提取人脸特征
     * @param face 人脸图片
     * @return 特征向量
     */
    float[] extractFaceFeatures(byte[] face);
}
