Launch a new agent that has access to the following tools: search_codebase, search_file, grep_code, search_symbol, list_dir, search_web, fetch_content, read_file. When you are searching for a keyword or file and are not confident that you will find the right match in the first few tries, use the agent tool to perform the search for you.
// 启动一个新的智能体，它可以使用以下工具：search_codebase, search_file, grep_code, search_symbol, list_dir, search_web, fetch_content, read_file。当你搜索关键词或文件但不确定能在前几次尝试中找到正确匹配时，使用智能体工具来为你执行搜索。

When to use the agent tool:
// 何时使用智能体工具：

- If you are searching for a keyword like "config" or "logger", or for questions like "which file does X?", the Agent tool is strongly recommended
// - 如果你在搜索像"config"或"logger"这样的关键词，或者询问"哪个文件做了X？"这样的问题，强烈推荐使用智能体工具

When NOT to use the Agent tool:
// 何时不要使用智能体工具：

- If you want to read a specific file path, use the read_file or grep_code tool instead of the Agent tool, to find the match more quickly
// - 如果你想读取特定的文件路径，使用read_file或grep_code工具而不是智能体工具，以便更快地找到匹配

- If you are searching for a specific class definition like "class Foo", use the search_symbol tool instead, to find the match more quickly
// - 如果你在搜索特定的类定义如"class Foo"，使用search_symbol工具，以便更快地找到匹配

- If you are searching for code within a specific file or set of 2-3 files, use the read_file tool instead of tis agent tool, to find the match more quickly
// - 如果你在特定文件或2-3个文件集合中搜索代码，使用read_file工具而不是智能体工具，以便更快地找到匹配

- Writing code and running bash commands (use other tools for that)
// - 编写代码和运行bash命令（请使用其他工具来完成这些任务）

- Other tasks that are not related to searching for a keyword or file
// - 其他与搜索关键词或文件无关的任务

Usage notes:
// 使用说明：

1. Launch multiple agents concurrently whenever possible, to maximize performance; to do that, use a single message with multiple tool uses
// 1. 尽可能同时启动多个智能体以最大化性能；要做到这一点，在单个消息中使用多个工具

2. When the agent is done, it will return a single message back to you. The result returned by the agent is not visible to the user. To show the user the result, you should send a text message back to the user with a concise summary of the result.
// 2. 当智能体完成任务时，它会向你返回一条消息。智能体返回的结果对用户不可见。要向用户显示结果，你应该向用户发送包含结果简要摘要的文本消息。

3. Each agent invocation is stateless. You will not be able to send additional messages to the agent, nor will the agent be able to communicate with you outside of its final report. Therefore, your prompt should contain a highly detailed task description for the agent to perform autonomously and you should specify exactly what information the agent should return back to you in its final and only message to you.
// 3. 每次智能体调用都是无状态的。你无法向智能体发送额外消息，智能体也无法在其最终报告之外与你通信。因此，你的提示应该包含详细的任务描述，让智能体能够自主执行，并且你应该明确指定智能体在其最终且唯一的消息中应该返回给你什么信息。

4. The agent's outputs should generally be trusted
// 4. 智能体的输出通常应该被信任

5. Clearly tell the agent whether you expect it to write code or just to do research (search, file reads, web fetches, etc.), since it is not aware of the user's intent
// 5. 明确告诉智能体你期望它编写代码还是仅进行研究（搜索、文件读取、网络获取等），因为它不知道用户的意图