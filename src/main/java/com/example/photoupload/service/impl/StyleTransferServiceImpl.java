package com.example.photoupload.service.impl;

import com.example.photoupload.dto.StyleTransferRequest;
import com.example.photoupload.dto.StyleTransferResponse;
import com.example.photoupload.service.StyleTransferService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 风格迁移服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class StyleTransferServiceImpl implements StyleTransferService {

    private static final Logger logger = LoggerFactory.getLogger(StyleTransferServiceImpl.class);

    @Value("${file.upload-dir}")
    private String uploadDir;

    // 内存存储迁移任务（实际项目中应使用数据库）
    private final Map<String, StyleTransferResponse> transferStorage = new ConcurrentHashMap<>();

    // 内存存储自定义风格
    private final Map<String, StyleTransferResponse.StyleInfo> customStyles = new ConcurrentHashMap<>();

    // 线程池用于异步处理
    private final ExecutorService executorService = Executors.newFixedThreadPool(4);

    @Override
    public StyleTransferResponse applyStyleTransfer(StyleTransferRequest request) {
        long startTime = System.currentTimeMillis();

        try {
            logger.info("开始风格迁移: contentImage={}, styleType={}",
                       request.getContentImagePath(), request.getStyleType());

            // 验证请求参数
            if (!validateTransferRequest(request)) {
                return StyleTransferResponse.error("请求参数验证失败");
            }

            // 生成迁移任务ID
            String transferId = "transfer_" + UUID.randomUUID().toString().replace("-", "");

            // 创建初始响应
            StyleTransferResponse response = StyleTransferResponse.processing(transferId, 0);
            response.setOriginalImagePath(request.getContentImagePath());
            response.setStyleType(request.getStyleType());
            response.setStyleName(getStyleName(request.getStyleType()));

            // 存储任务
            transferStorage.put(transferId, response);

            if (request.isAsync()) {
                // 异步处理
                executorService.submit(() -> processStyleTransferAsync(transferId, request));
                response.setMessage("任务已提交，正在异步处理");
            } else {
                // 同步处理
                response = processStyleTransferSync(transferId, request);
            }

            response.setProcessingTime(System.currentTimeMillis() - startTime);

            return response;

        } catch (Exception e) {
            logger.error("风格迁移失败", e);
            return StyleTransferResponse.error("风格迁移失败: " + e.getMessage());
        }
    }

    @Override
    public StyleTransferResponse uploadCustomStyle(MultipartFile file, String styleName, String description) {
        try {
            logger.info("上传自定义风格: styleName={}, fileSize={}", styleName, file.getSize());

            // 验证文件
            if (file.isEmpty()) {
                return StyleTransferResponse.error("文件不能为空");
            }

            if (!isValidImageFile(file)) {
                return StyleTransferResponse.error("不支持的图片格式");
            }

            // 生成风格ID
            String styleId = "custom_" + UUID.randomUUID().toString().replace("-", "");

            // 保存文件
            String fileName = styleId + "_" + file.getOriginalFilename();
            Path filePath = Paths.get(uploadDir, "styles", fileName);
            Files.createDirectories(filePath.getParent());
            Files.copy(file.getInputStream(), filePath);

            // 创建风格信息
            StyleTransferResponse.StyleInfo styleInfo = new StyleTransferResponse.StyleInfo();
            styleInfo.setStyleId(styleId);
            styleInfo.setStyleName(styleName);
            styleInfo.setStyleType("custom");
            styleInfo.setDescription(description);
            styleInfo.setPreviewUrl("/api/files/styles/" + fileName);
            styleInfo.setCustom(true);
            styleInfo.setCreatedAt(LocalDateTime.now());
            styleInfo.setUsageCount(0);
            styleInfo.setAverageRating(0.0f);

            // 存储风格信息
            customStyles.put(styleId, styleInfo);

            StyleTransferResponse response = StyleTransferResponse.success(styleId, "自定义风格上传成功");
            response.setStyleType("custom");
            response.setStyleName(styleName);

            return response;

        } catch (Exception e) {
            logger.error("上传自定义风格失败", e);
            return StyleTransferResponse.error("上传失败: " + e.getMessage());
        }
    }

    @Override
    public List<StyleTransferResponse.StyleInfo> getAvailableStyles() {
        logger.info("获取可用风格列表");

        List<StyleTransferResponse.StyleInfo> styles = new ArrayList<>();

        // 添加预定义风格
        styles.addAll(getBuiltInStyles());

        // 添加自定义风格
        styles.addAll(customStyles.values());

        return styles;
    }

    @Override
    public List<StyleTransferResponse> getTransferHistory() {
        logger.info("获取风格迁移历史");

        return transferStorage.values().stream()
                .filter(response -> "completed".equals(response.getStatus()) || "failed".equals(response.getStatus()))
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());
    }

    @Override
    public Resource getTransferResult(String transferId) {
        try {
            logger.info("获取风格迁移结果: transferId={}", transferId);

            StyleTransferResponse transfer = transferStorage.get(transferId);
            if (transfer == null || transfer.getStyledImagePath() == null) {
                throw new RuntimeException("迁移结果不存在");
            }

            Path filePath = Paths.get(transfer.getStyledImagePath());
            if (!Files.exists(filePath)) {
                throw new RuntimeException("结果文件不存在");
            }

            return new FileSystemResource(filePath);

        } catch (Exception e) {
            logger.error("获取风格迁移结果失败: transferId={}", transferId, e);
            throw new RuntimeException("获取结果失败: " + e.getMessage());
        }
    }

    @Override
    public List<StyleTransferResponse> batchStyleTransfer(List<StyleTransferRequest> requests) {
        logger.info("批量风格迁移: count={}", requests.size());

        return requests.stream()
                .map(this::applyStyleTransfer)
                .collect(Collectors.toList());
    }

    @Override
    public StyleTransferResponse previewStyleTransfer(StyleTransferRequest request) {
        try {
            logger.info("预览风格迁移效果: contentImage={}, styleType={}",
                       request.getContentImagePath(), request.getStyleType());

            // 生成预览ID
            String previewId = "preview_" + UUID.randomUUID().toString().substring(0, 8);

            // 创建低分辨率预览（模拟）
            String previewPath = generatePreviewImage(request.getContentImagePath(), request.getStyleType());

            StyleTransferResponse response = StyleTransferResponse.success(previewId, "预览生成成功");
            response.setOriginalImagePath(request.getContentImagePath());
            response.setPreviewUrl("/api/files/previews/" + previewId + ".jpg");
            response.setStyleType(request.getStyleType());
            response.setStyleName(getStyleName(request.getStyleType()));
            response.setDimensions("256x256");
            response.setProcessingTime(500); // 预览处理时间较短

            return response;

        } catch (Exception e) {
            logger.error("预览风格迁移效果失败", e);
            return StyleTransferResponse.error("预览失败: " + e.getMessage());
        }
    }

    @Override
    public StyleTransferResponse getTransferProgress(String transferId) {
        logger.info("获取风格迁移进度: transferId={}", transferId);

        StyleTransferResponse transfer = transferStorage.get(transferId);
        if (transfer == null) {
            return StyleTransferResponse.error("迁移任务不存在");
        }

        return transfer;
    }

    @Override
    public boolean cancelTransfer(String transferId) {
        try {
            logger.info("取消风格迁移: transferId={}", transferId);

            StyleTransferResponse transfer = transferStorage.get(transferId);
            if (transfer == null) {
                return false;
            }

            if ("processing".equals(transfer.getStatus())) {
                transfer.setStatus("cancelled");
                transfer.setMessage("任务已取消");
                transfer.setCompletedAt(LocalDateTime.now());
                return true;
            }

            return false;

        } catch (Exception e) {
            logger.error("取消风格迁移失败: transferId={}", transferId, e);
            return false;
        }
    }

    @Override
    public boolean deleteTransferResult(String transferId) {
        try {
            logger.info("删除风格迁移结果: transferId={}", transferId);

            StyleTransferResponse transfer = transferStorage.get(transferId);
            if (transfer == null) {
                return false;
            }

            // 删除结果文件
            if (transfer.getStyledImagePath() != null) {
                Path filePath = Paths.get(transfer.getStyledImagePath());
                Files.deleteIfExists(filePath);
            }

            // 删除缩略图
            if (transfer.getThumbnailUrl() != null) {
                String thumbnailPath = transfer.getThumbnailUrl().replace("/api/files/", uploadDir + "/");
                Files.deleteIfExists(Paths.get(thumbnailPath));
            }

            // 从存储中移除
            transferStorage.remove(transferId);

            return true;

        } catch (Exception e) {
            logger.error("删除风格迁移结果失败: transferId={}", transferId, e);
            return false;
        }
    }

    @Override
    public StyleTransferResponse getTransferStatistics() {
        logger.info("获取风格迁移统计信息");

        StyleTransferResponse response = new StyleTransferResponse();
        response.setSuccess(true);
        response.setMessage("统计信息获取成功");

        // 生成统计信息
        StyleTransferResponse.TransferStatistics stats = new StyleTransferResponse.TransferStatistics();

        List<StyleTransferResponse> allTransfers = new ArrayList<>(transferStorage.values());

        stats.setTotalTransfers(allTransfers.size());
        stats.setSuccessfulTransfers((int) allTransfers.stream().filter(t -> "completed".equals(t.getStatus())).count());
        stats.setFailedTransfers((int) allTransfers.stream().filter(t -> "failed".equals(t.getStatus())).count());

        // 计算平均处理时间
        OptionalDouble avgTime = allTransfers.stream()
                .filter(t -> "completed".equals(t.getStatus()))
                .mapToLong(StyleTransferResponse::getProcessingTime)
                .average();
        stats.setAverageProcessingTime(avgTime.isPresent() ? (long) avgTime.getAsDouble() : 0);

        // 最受欢迎的风格
        Map<String, Long> styleCount = allTransfers.stream()
                .filter(t -> t.getStyleType() != null)
                .collect(Collectors.groupingBy(StyleTransferResponse::getStyleType, Collectors.counting()));

        String mostPopular = styleCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("unknown");
        stats.setMostPopularStyle(mostPopular);

        // 时间分布统计
        LocalDateTime now = LocalDateTime.now();
        stats.setTodayTransfers((int) allTransfers.stream()
                .filter(t -> t.getCreatedAt().toLocalDate().equals(now.toLocalDate()))
                .count());

        stats.setWeeklyTransfers((int) allTransfers.stream()
                .filter(t -> t.getCreatedAt().isAfter(now.minusWeeks(1)))
                .count());

        stats.setMonthlyTransfers((int) allTransfers.stream()
                .filter(t -> t.getCreatedAt().isAfter(now.minusMonths(1)))
                .count());

        // 风格分布
        Map<String, Integer> styleDistribution = styleCount.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().intValue()));
        stats.setStyleDistribution(styleDistribution);

        response.setStatistics(stats);

        return response;
    }

    @Override
    public List<StyleTransferResponse.StyleInfo> recommendStyles(String imagePath) {
        logger.info("推荐风格: imagePath={}", imagePath);

        List<StyleTransferResponse.StyleInfo> recommendations = new ArrayList<>();

        try {
            // 分析图片内容（简化实现）
            String imageType = analyzeImageType(imagePath);

            // 基于图片类型推荐风格
            List<StyleTransferResponse.StyleInfo> allStyles = getAvailableStyles();

            for (StyleTransferResponse.StyleInfo style : allStyles) {
                float score = calculateRecommendationScore(imageType, style);
                if (score > 0.5f) {
                    style.setRecommendationScore(score);
                    recommendations.add(style);
                }
            }

            // 按推荐分数排序
            recommendations.sort((a, b) -> Float.compare(b.getRecommendationScore(), a.getRecommendationScore()));

            // 返回前5个推荐
            return recommendations.stream().limit(5).collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("推荐风格失败: imagePath={}", imagePath, e);
            return new ArrayList<>();
        }
    }

    @Override
    public StyleTransferResponse analyzeImageContent(String imagePath) {
        logger.info("分析图片内容: imagePath={}", imagePath);

        StyleTransferResponse response = new StyleTransferResponse();
        response.setSuccess(true);
        response.setMessage("图片内容分析完成");

        try {
            // 模拟图片内容分析
            Map<String, Object> analysis = new HashMap<>();
            analysis.put("imageType", analyzeImageType(imagePath));
            analysis.put("dominantColors", Arrays.asList("#FF5733", "#33FF57", "#3357FF"));
            analysis.put("complexity", "medium");
            analysis.put("brightness", 0.7f);
            analysis.put("contrast", 0.8f);
            analysis.put("saturation", 0.6f);
            analysis.put("objects", Arrays.asList("person", "background", "nature"));
            analysis.put("scene", "outdoor");

            response.setMetadata(analysis);

        } catch (Exception e) {
            logger.error("分析图片内容失败: imagePath={}", imagePath, e);
            response.setSuccess(false);
            response.setMessage("分析失败: " + e.getMessage());
        }

        return response;
    }

    @Override
    public boolean validateTransferRequest(StyleTransferRequest request) {
        if (request == null) {
            return false;
        }

        if (request.getContentImagePath() == null || request.getContentImagePath().trim().isEmpty()) {
            return false;
        }

        if (request.getStyleType() == null || request.getStyleType().trim().isEmpty()) {
            return false;
        }

        // 验证文件是否存在
        Path imagePath = Paths.get(uploadDir, request.getContentImagePath());
        if (!Files.exists(imagePath)) {
            return false;
        }

        // 验证风格强度范围
        if (request.getStyleStrength() < 0.0f || request.getStyleStrength() > 1.0f) {
            return false;
        }

        return true;
    }

    @Override
    public StyleTransferRequest optimizeTransferParameters(StyleTransferRequest request) {
        logger.info("优化风格迁移参数");

        // 根据图片大小调整参数
        try {
            Path imagePath = Paths.get(uploadDir, request.getContentImagePath());
            BufferedImage image = ImageIO.read(imagePath.toFile());

            int width = image.getWidth();
            int height = image.getHeight();
            int pixels = width * height;

            // 根据图片大小调整迭代次数
            if (request.getParameters() != null) {
                if (pixels > 2000000) { // 大图片
                    request.getParameters().setIterations(300);
                    request.getParameters().setLearningRate(0.01f);
                } else if (pixels < 500000) { // 小图片
                    request.getParameters().setIterations(800);
                    request.getParameters().setLearningRate(0.03f);
                }
            }

            // 根据输出质量调整参数
            if ("high".equals(request.getOutputQuality())) {
                request.setCompressionQuality(95);
            } else if ("medium".equals(request.getOutputQuality())) {
                request.setCompressionQuality(85);
            } else {
                request.setCompressionQuality(75);
            }

        } catch (Exception e) {
            logger.error("优化参数失败", e);
        }

        return request;
    }

    @Override
    public String generateThumbnail(String transferId) {
        try {
            logger.info("生成缩略图: transferId={}", transferId);

            StyleTransferResponse transfer = transferStorage.get(transferId);
            if (transfer == null || transfer.getStyledImagePath() == null) {
                return null;
            }

            // 生成缩略图路径
            String thumbnailPath = uploadDir + "/thumbnails/" + transferId + "_thumb.jpg";
            Path thumbnailDir = Paths.get(uploadDir, "thumbnails");
            Files.createDirectories(thumbnailDir);

            // 读取原图并生成缩略图
            BufferedImage originalImage = ImageIO.read(new File(transfer.getStyledImagePath()));
            BufferedImage thumbnail = createThumbnail(originalImage, 200, 200);

            // 保存缩略图
            ImageIO.write(thumbnail, "jpg", new File(thumbnailPath));

            String thumbnailUrl = "/api/files/thumbnails/" + transferId + "_thumb.jpg";
            transfer.setThumbnailUrl(thumbnailUrl);

            return thumbnailUrl;

        } catch (Exception e) {
            logger.error("生成缩略图失败: transferId={}", transferId, e);
            return null;
        }
    }

    @Override
    public float calculateStyleSimilarity(String image1Path, String image2Path) {
        logger.info("计算风格相似度: image1={}, image2={}", image1Path, image2Path);

        // 模拟风格相似度计算
        return 0.75f + (float)(Math.random() * 0.25);
    }

    @Override
    public float evaluateTransferQuality(String originalPath, String styledPath) {
        logger.info("评估风格迁移质量: original={}, styled={}", originalPath, styledPath);

        try {
            // 模拟质量评估
            BufferedImage original = ImageIO.read(new File(originalPath));
            BufferedImage styled = ImageIO.read(new File(styledPath));

            // 简单的质量评估（实际应该使用更复杂的算法）
            float structuralSimilarity = calculateStructuralSimilarity(original, styled);
            float colorHarmony = calculateColorHarmony(styled);
            float sharpness = calculateSharpness(styled);

            return (structuralSimilarity * 0.4f + colorHarmony * 0.3f + sharpness * 0.3f);

        } catch (Exception e) {
            logger.error("评估质量失败", e);
            return 0.5f; // 默认分数
        }
    }

    @Override
    public int cleanupExpiredResults(int daysOld) {
        logger.info("清理过期结果: daysOld={}", daysOld);

        int cleanedCount = 0;
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);

        Iterator<Map.Entry<String, StyleTransferResponse>> iterator = transferStorage.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, StyleTransferResponse> entry = iterator.next();
            StyleTransferResponse transfer = entry.getValue();

            if (transfer.getCreatedAt().isBefore(cutoffDate)) {
                try {
                    // 删除文件
                    if (transfer.getStyledImagePath() != null) {
                        Files.deleteIfExists(Paths.get(transfer.getStyledImagePath()));
                    }
                    if (transfer.getThumbnailUrl() != null) {
                        String thumbnailPath = transfer.getThumbnailUrl().replace("/api/files/", uploadDir + "/");
                        Files.deleteIfExists(Paths.get(thumbnailPath));
                    }

                    // 从存储中移除
                    iterator.remove();
                    cleanedCount++;

                } catch (Exception e) {
                    logger.error("清理文件失败: transferId={}", entry.getKey(), e);
                }
            }
        }

        logger.info("清理完成，共清理 {} 个过期结果", cleanedCount);
        return cleanedCount;
    }

    @Override
    public String exportTransferConfig(String transferId) {
        logger.info("导出迁移配置: transferId={}", transferId);

        StyleTransferResponse transfer = transferStorage.get(transferId);
        if (transfer == null) {
            return null;
        }

        // 简化的配置导出（实际应该使用JSON序列化）
        Map<String, Object> config = new HashMap<>();
        config.put("styleType", transfer.getStyleType());
        config.put("parameters", transfer.getParameters());
        config.put("dimensions", transfer.getDimensions());
        config.put("qualityScore", transfer.getQualityScore());

        return config.toString(); // 实际应该返回JSON字符串
    }

    @Override
    public StyleTransferRequest importTransferConfig(String configJson) {
        logger.info("导入迁移配置");

        // 简化的配置导入（实际应该使用JSON反序列化）
        StyleTransferRequest request = new StyleTransferRequest();
        request.setStyleType("van_gogh"); // 默认风格
        request.setStyleStrength(0.8f);
        request.setOutputQuality("high");

        return request;
    }

    // 私有辅助方法
    private StyleTransferResponse processStyleTransferSync(String transferId, StyleTransferRequest request) {
        try {
            logger.info("同步处理风格迁移: transferId={}", transferId);

            StyleTransferResponse response = transferStorage.get(transferId);

            // 模拟处理过程
            for (int i = 0; i <= 100; i += 20) {
                response.setProgress(i);
                response.setMessage("处理中... " + i + "%");
                Thread.sleep(200); // 模拟处理时间
            }

            // 生成输出文件路径
            String outputFileName = transferId + "_styled.jpg";
            String outputPath = uploadDir + "/styled/" + outputFileName;

            // 创建输出目录
            Path outputDir = Paths.get(uploadDir, "styled");
            Files.createDirectories(outputDir);

            // 模拟风格迁移处理（实际应该调用AI模型）
            processImageWithStyle(request.getContentImagePath(), request.getStyleType(), outputPath);

            // 更新响应
            response.setStatus("completed");
            response.setProgress(100);
            response.setMessage("风格迁移完成");
            response.setStyledImagePath(outputPath);
            response.setStyledImageUrl("/api/files/styled/" + outputFileName);
            response.setCompletedAt(LocalDateTime.now());
            response.setFileSize(new File(outputPath).length());
            response.setQualityScore(evaluateTransferQuality(request.getContentImagePath(), outputPath));
            response.setStyleSimilarity(0.85f + (float)(Math.random() * 0.15));

            // 生成缩略图
            generateThumbnail(transferId);

            return response;

        } catch (Exception e) {
            logger.error("同步处理失败: transferId={}", transferId, e);
            StyleTransferResponse response = transferStorage.get(transferId);
            response.setStatus("failed");
            response.setMessage("处理失败: " + e.getMessage());
            response.setErrorMessage(e.getMessage());
            return response;
        }
    }

    private void processStyleTransferAsync(String transferId, StyleTransferRequest request) {
        try {
            logger.info("异步处理风格迁移: transferId={}", transferId);

            StyleTransferResponse response = transferStorage.get(transferId);
            response.setStatus("processing");

            // 异步处理逻辑与同步类似
            processStyleTransferSync(transferId, request);

        } catch (Exception e) {
            logger.error("异步处理失败: transferId={}", transferId, e);
            StyleTransferResponse response = transferStorage.get(transferId);
            response.setStatus("failed");
            response.setMessage("异步处理失败: " + e.getMessage());
        }
    }

    private void processImageWithStyle(String contentPath, String styleType, String outputPath) throws IOException {
        logger.info("处理图片风格: contentPath={}, styleType={}, outputPath={}", contentPath, styleType, outputPath);

        // 读取原图
        BufferedImage originalImage = ImageIO.read(new File(uploadDir + "/" + contentPath));

        // 模拟风格迁移处理
        BufferedImage styledImage = applyStyleEffect(originalImage, styleType);

        // 保存结果
        ImageIO.write(styledImage, "jpg", new File(outputPath));
    }

    private BufferedImage applyStyleEffect(BufferedImage original, String styleType) {
        // 简化的风格效果模拟
        BufferedImage styled = new BufferedImage(original.getWidth(), original.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = styled.createGraphics();

        // 根据风格类型应用不同效果
        switch (styleType.toLowerCase()) {
            case "van_gogh":
                // 模拟梵高风格（增加饱和度和对比度）
                g2d.drawImage(original, 0, 0, null);
                // 这里应该实现真正的风格迁移算法
                break;
            case "picasso":
                // 模拟毕加索风格
                g2d.drawImage(original, 0, 0, null);
                break;
            default:
                g2d.drawImage(original, 0, 0, null);
        }

        g2d.dispose();
        return styled;
    }

    private String generatePreviewImage(String contentPath, String styleType) throws IOException {
        // 生成低分辨率预览
        BufferedImage original = ImageIO.read(new File(uploadDir + "/" + contentPath));
        BufferedImage preview = createThumbnail(original, 256, 256);
        BufferedImage styledPreview = applyStyleEffect(preview, styleType);

        String previewPath = uploadDir + "/previews/preview_" + UUID.randomUUID().toString().substring(0, 8) + ".jpg";
        Path previewDir = Paths.get(uploadDir, "previews");
        Files.createDirectories(previewDir);

        ImageIO.write(styledPreview, "jpg", new File(previewPath));
        return previewPath;
    }

    private BufferedImage createThumbnail(BufferedImage original, int maxWidth, int maxHeight) {
        int originalWidth = original.getWidth();
        int originalHeight = original.getHeight();

        // 计算缩放比例
        double scale = Math.min((double) maxWidth / originalWidth, (double) maxHeight / originalHeight);
        int newWidth = (int) (originalWidth * scale);
        int newHeight = (int) (originalHeight * scale);

        BufferedImage thumbnail = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = thumbnail.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(original, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        return thumbnail;
    }

    private boolean isValidImageFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType != null && (
                contentType.equals("image/jpeg") ||
                contentType.equals("image/png") ||
                contentType.equals("image/gif") ||
                contentType.equals("image/bmp")
        );
    }

    private String getStyleName(String styleType) {
        Map<String, String> styleNames = new HashMap<>();
        styleNames.put("van_gogh", "梵高风格");
        styleNames.put("picasso", "毕加索风格");
        styleNames.put("monet", "莫奈风格");
        styleNames.put("da_vinci", "达芬奇风格");
        styleNames.put("abstract", "抽象风格");
        styleNames.put("watercolor", "水彩风格");
        styleNames.put("oil_painting", "油画风格");
        styleNames.put("sketch", "素描风格");

        return styleNames.getOrDefault(styleType, styleType);
    }

    private List<StyleTransferResponse.StyleInfo> getBuiltInStyles() {
        List<StyleTransferResponse.StyleInfo> styles = new ArrayList<>();

        String[] builtInStyles = {"van_gogh", "picasso", "monet", "da_vinci", "abstract", "watercolor", "oil_painting", "sketch"};
        String[] artists = {"梵高", "毕加索", "莫奈", "达芬奇", "现代艺术家", "水彩画家", "油画大师", "素描艺术家"};
        String[] periods = {"后印象派", "立体主义", "印象派", "文艺复兴", "现代", "传统", "古典", "现代"};

        for (int i = 0; i < builtInStyles.length; i++) {
            StyleTransferResponse.StyleInfo style = new StyleTransferResponse.StyleInfo();
            style.setStyleId("builtin_" + builtInStyles[i]);
            style.setStyleName(getStyleName(builtInStyles[i]));
            style.setStyleType(builtInStyles[i]);
            style.setDescription("经典的" + getStyleName(builtInStyles[i]) + "艺术风格");
            style.setPreviewUrl("/api/styles/previews/" + builtInStyles[i] + ".jpg");
            style.setTags(Arrays.asList("经典", "艺术", "绘画"));
            style.setArtist(artists[i]);
            style.setPeriod(periods[i]);
            style.setRecommendationScore(0.8f + (float)(Math.random() * 0.2));
            style.setUsageCount(100 + (int)(Math.random() * 500));
            style.setAverageRating(4.0f + (float)(Math.random() * 1.0));
            style.setCustom(false);
            style.setCreatedAt(LocalDateTime.now().minusMonths(6));

            styles.add(style);
        }

        return styles;
    }

    private String analyzeImageType(String imagePath) {
        // 简化的图片类型分析
        String[] portraitKeywords = {"person", "face", "portrait"};
        String[] landscapeKeywords = {"landscape", "nature", "outdoor"};
        String[] abstractKeywords = {"abstract", "pattern", "texture"};

        // 模拟分析结果
        double random = Math.random();
        if (random < 0.4) {
            return "portrait";
        } else if (random < 0.7) {
            return "landscape";
        } else {
            return "abstract";
        }
    }

    private float calculateRecommendationScore(String imageType, StyleTransferResponse.StyleInfo style) {
        // 基于图片类型和风格匹配度计算推荐分数
        Map<String, Map<String, Float>> compatibilityMatrix = new HashMap<>();

        Map<String, Float> portraitCompatibility = new HashMap<>();
        portraitCompatibility.put("van_gogh", 0.9f);
        portraitCompatibility.put("da_vinci", 0.95f);
        portraitCompatibility.put("oil_painting", 0.85f);
        portraitCompatibility.put("sketch", 0.8f);
        compatibilityMatrix.put("portrait", portraitCompatibility);

        Map<String, Float> landscapeCompatibility = new HashMap<>();
        landscapeCompatibility.put("monet", 0.95f);
        landscapeCompatibility.put("van_gogh", 0.9f);
        landscapeCompatibility.put("watercolor", 0.85f);
        landscapeCompatibility.put("oil_painting", 0.8f);
        compatibilityMatrix.put("landscape", landscapeCompatibility);

        Map<String, Float> abstractCompatibility = new HashMap<>();
        abstractCompatibility.put("picasso", 0.95f);
        abstractCompatibility.put("abstract", 0.9f);
        abstractCompatibility.put("van_gogh", 0.7f);
        compatibilityMatrix.put("abstract", abstractCompatibility);

        Map<String, Float> typeCompatibility = compatibilityMatrix.get(imageType);
        if (typeCompatibility != null) {
            return typeCompatibility.getOrDefault(style.getStyleType(), 0.5f);
        }

        return 0.5f; // 默认分数
    }

    private float calculateStructuralSimilarity(BufferedImage img1, BufferedImage img2) {
        // 简化的结构相似度计算
        return 0.8f + (float)(Math.random() * 0.2);
    }

    private float calculateColorHarmony(BufferedImage image) {
        // 简化的色彩和谐度计算
        return 0.7f + (float)(Math.random() * 0.3);
    }

    private float calculateSharpness(BufferedImage image) {
        // 简化的清晰度计算
        return 0.75f + (float)(Math.random() * 0.25);
    }
}