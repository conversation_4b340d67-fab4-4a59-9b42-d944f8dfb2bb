<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 PhotoMagic - 酷炫照片处理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }

        .nav-logo i {
            margin-right: 0.5rem;
            color: #ffd700;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .main-container {
            margin-top: 80px;
            padding: 2rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .section h2 {
            margin-bottom: 1.5rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .upload-area {
            text-align: center;
        }

        .upload-zone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 3rem;
            transition: all 0.3s ease;
            cursor: pointer;
            background: linear-gradient(45deg, #f8f9ff, #e8f0ff);
        }

        .upload-zone:hover {
            border-color: #764ba2;
            background: linear-gradient(45deg, #e8f0ff, #f0f8ff);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }

        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
        }

        .realtime-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-top: 1rem;
        }

        .image-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .preview-original, .preview-processed {
            text-align: center;
        }

        .preview-original h4, .preview-processed h4 {
            margin-bottom: 0.5rem;
            color: #555;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            max-width: 100%;
            height: auto;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .preview-processed {
            position: relative;
        }

        .processing-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 10px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .controls-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
        }

        .control-group {
            margin-bottom: 2rem;
        }

        .control-group h4 {
            margin-bottom: 1rem;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
        }

        .filter-btn {
            padding: 0.5rem;
            border: 2px solid #dee2e6;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-btn:hover, .filter-btn.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
            transform: scale(1.05);
        }

        .slider-controls {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .slider-item {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .slider-item label {
            min-width: 60px;
            font-weight: 500;
        }

        .slider-item input[type="range"] {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #dee2e6;
            outline: none;
            -webkit-appearance: none;
        }

        .slider-item input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }

        .slider-item span {
            min-width: 30px;
            text-align: center;
            font-weight: bold;
            color: #667eea;
        }

        .crop-controls, .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .gallery-container {
            margin-top: 1rem;
        }

        .gallery-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            justify-content: center;
        }

        .filter-tag {
            padding: 0.5rem 1rem;
            border: 2px solid #dee2e6;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tag.active, .filter-tag:hover {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .gallery-item {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .gallery-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .gallery-item-info {
            padding: 1rem;
        }

        .ai-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            margin-top: 1rem;
        }

        .ai-drop-zone {
            border: 3px dashed #28a745;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: linear-gradient(45deg, #f8fff8, #e8f8e8);
        }

        .ai-drop-zone:hover {
            border-color: #20c997;
            background: linear-gradient(45deg, #e8f8e8, #f0fff0);
        }

        .ai-drop-zone i {
            font-size: 3rem;
            color: #28a745;
            margin-bottom: 1rem;
        }

        .ai-results {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .ai-image img {
            width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .analysis-tabs {
            display: flex;
            margin-bottom: 1rem;
            border-bottom: 2px solid #dee2e6;
        }

        .tab-btn {
            padding: 0.75rem 1rem;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active, .tab-btn:hover {
            border-bottom-color: #667eea;
            color: #667eea;
        }

        .tab-content {
            display: none;
            padding: 1rem 0;
        }

        .tab-content.active {
            display: block;
        }

        .notification-container {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 1001;
        }

        .notification {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border-left: 4px solid #28a745;
            animation: slideInRight 0.3s ease;
        }

        .notification.error {
            border-left-color: #dc3545;
        }

        .notification.warning {
            border-left-color: #ffc107;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .realtime-container,
            .ai-container {
                grid-template-columns: 1fr;
            }
            
            .image-preview {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-camera-retro"></i>
                <span>PhotoMagic</span>
            </div>
            <div class="nav-menu">
                <a href="#upload" class="nav-link"><i class="fas fa-upload"></i> 上传</a>
                <a href="#realtime" class="nav-link"><i class="fas fa-magic"></i> 实时处理</a>
                <a href="#gallery" class="nav-link"><i class="fas fa-images"></i> 相册</a>
                <a href="#ai" class="nav-link"><i class="fas fa-brain"></i> AI分析</a>
            </div>
        </div>
    </nav>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 上传区域 -->
        <section id="upload" class="section animate__animated animate__fadeInUp">
            <h2><i class="fas fa-cloud-upload-alt"></i> 图片上传</h2>
            <div class="upload-area">
                <div class="upload-zone" id="uploadZone">
                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                    <h3>拖拽图片到这里或点击上传</h3>
                    <p>支持 JPG, PNG, GIF, WebP 格式，最大 10MB</p>
                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                        <i class="fas fa-plus"></i> 选择文件
                    </button>
                </div>
                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span class="progress-text" id="progressText">0%</span>
                </div>
            </div>
        </section>

        <!-- 实时处理区域 -->
        <section id="realtime" class="section animate__animated animate__fadeInUp">
            <h2><i class="fas fa-magic"></i> 实时图像处理</h2>
            <div class="realtime-container">
                <div class="image-preview">
                    <div class="preview-original">
                        <h4>原图</h4>
                        <canvas id="originalCanvas" width="400" height="300"></canvas>
                    </div>
                    <div class="preview-processed">
                        <h4>预览效果</h4>
                        <canvas id="previewCanvas" width="400" height="300"></canvas>
                        <div class="processing-overlay" id="processingOverlay" style="display: none;">
                            <div class="spinner"></div>
                            <span>处理中...</span>
                        </div>
                    </div>
                </div>

                <div class="controls-panel">
                    <!-- 滤镜控制 -->
                    <div class="control-group">
                        <h4><i class="fas fa-palette"></i> 滤镜效果</h4>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="none">原图</button>
                            <button class="filter-btn" data-filter="vintage">复古</button>
                            <button class="filter-btn" data-filter="sepia">棕褐</button>
                            <button class="filter-btn" data-filter="grayscale">黑白</button>
                            <button class="filter-btn" data-filter="blur">模糊</button>
                            <button class="filter-btn" data-filter="sharpen">锐化</button>
                        </div>
                    </div>

                    <!-- 调色控制 -->
                    <div class="control-group">
                        <h4><i class="fas fa-sliders-h"></i> 图像调整</h4>
                        <div class="slider-controls">
                            <div class="slider-item">
                                <label>亮度</label>
                                <input type="range" id="brightnessSlider" min="-0.5" max="0.5" step="0.1" value="0">
                                <span id="brightnessValue">0</span>
                            </div>
                            <div class="slider-item">
                                <label>对比度</label>
                                <input type="range" id="contrastSlider" min="-0.5" max="0.5" step="0.1" value="0">
                                <span id="contrastValue">0</span>
                            </div>
                            <div class="slider-item">
                                <label>饱和度</label>
                                <input type="range" id="saturationSlider" min="-0.5" max="0.5" step="0.1" value="0">
                                <span id="saturationValue">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- 裁剪控制 -->
                    <div class="control-group">
                        <h4><i class="fas fa-crop"></i> 裁剪工具</h4>
                        <div class="crop-controls">
                            <button class="btn btn-secondary" id="enableCropBtn">启用裁剪</button>
                            <button class="btn btn-success" id="applyCropBtn" style="display: none;">应用裁剪</button>
                            <button class="btn btn-danger" id="cancelCropBtn" style="display: none;">取消裁剪</button>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="control-group">
                        <div class="action-buttons">
                            <button class="btn btn-primary" id="saveBtn">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <button class="btn btn-secondary" id="resetBtn">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                            <button class="btn btn-info" id="downloadBtn">
                                <i class="fas fa-download"></i> 下载
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 图片库区域 -->
        <section id="gallery" class="section animate__animated animate__fadeInUp">
            <h2><i class="fas fa-images"></i> 智能相册</h2>
            <div class="gallery-container">
                <div class="gallery-filters">
                    <button class="filter-tag active" data-filter="all">全部</button>
                    <button class="filter-tag" data-filter="recent">最近</button>
                    <button class="filter-tag" data-filter="favorites">收藏</button>
                    <button class="filter-tag" data-filter="processed">已处理</button>
                </div>
                <div class="gallery-grid" id="galleryGrid">
                    <!-- 动态加载图片 -->
                </div>
            </div>
        </section>

        <!-- AI分析区域 -->
        <section id="ai" class="section animate__animated animate__fadeInUp">
            <h2><i class="fas fa-brain"></i> AI智能分析</h2>
            <div class="ai-container">
                <div class="ai-upload">
                    <div class="ai-drop-zone" id="aiDropZone">
                        <i class="fas fa-robot"></i>
                        <h3>上传图片进行AI分析</h3>
                        <p>支持物体识别、文字提取、色彩分析等</p>
                        <input type="file" id="aiFileInput" accept="image/*" style="display: none;">
                        <button class="btn btn-primary" onclick="document.getElementById('aiFileInput').click()">
                            <i class="fas fa-upload"></i> 选择图片
                        </button>
                    </div>
                </div>
                <div class="ai-results" id="aiResults" style="display: none;">
                    <div class="ai-image">
                        <img id="aiImage" alt="AI分析图片">
                    </div>
                    <div class="ai-analysis">
                        <div class="analysis-tabs">
                            <button class="tab-btn active" data-tab="objects">物体识别</button>
                            <button class="tab-btn" data-tab="text">文字提取</button>
                            <button class="tab-btn" data-tab="colors">色彩分析</button>
                            <button class="tab-btn" data-tab="labels">智能标签</button>
                        </div>
                        <div class="analysis-content">
                            <div class="tab-content active" id="objects-content">
                                <div class="objects-list"></div>
                            </div>
                            <div class="tab-content" id="text-content">
                                <div class="extracted-text"></div>
                            </div>
                            <div class="tab-content" id="colors-content">
                                <div class="color-palette"></div>
                            </div>
                            <div class="tab-content" id="labels-content">
                                <div class="labels-list"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 通知系统 -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.6.1/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
    <script>
        // 基础应用逻辑
        class PhotoMagicApp {
            constructor() {
                this.stompClient = null;
                this.currentImage = null;
                this.currentFilter = 'none';
                this.cropMode = false;
                this.cropSelection = null;

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.connectWebSocket();
                this.loadGallery();
            }

            setupEventListeners() {
                // 文件上传
                const fileInput = document.getElementById('fileInput');
                const uploadZone = document.getElementById('uploadZone');

                fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
                uploadZone.addEventListener('dragover', (e) => this.handleDragOver(e));
                uploadZone.addEventListener('drop', (e) => this.handleDrop(e));
                uploadZone.addEventListener('click', () => fileInput.click());

                // 滤镜按钮
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => this.applyFilter(e.target.dataset.filter));
                });

                // 滑块控制
                ['brightness', 'contrast', 'saturation'].forEach(type => {
                    const slider = document.getElementById(type + 'Slider');
                    const value = document.getElementById(type + 'Value');
                    slider.addEventListener('input', (e) => {
                        value.textContent = e.target.value;
                        this.applyAdjustment();
                    });
                });

                // 裁剪控制
                document.getElementById('enableCropBtn').addEventListener('click', () => this.enableCrop());
                document.getElementById('applyCropBtn').addEventListener('click', () => this.applyCrop());
                document.getElementById('cancelCropBtn').addEventListener('click', () => this.cancelCrop());

                // 操作按钮
                document.getElementById('saveBtn').addEventListener('click', () => this.saveImage());
                document.getElementById('resetBtn').addEventListener('click', () => this.resetImage());
                document.getElementById('downloadBtn').addEventListener('click', () => this.downloadImage());

                // AI分析
                const aiFileInput = document.getElementById('aiFileInput');
                aiFileInput.addEventListener('change', (e) => this.handleAIAnalysis(e));

                // 标签页切换
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
                });
            }

            connectWebSocket() {
                const socket = new SockJS('/ws');
                this.stompClient = Stomp.over(socket);

                this.stompClient.connect({}, (frame) => {
                    console.log('WebSocket连接成功: ' + frame);

                    // 订阅实时处理结果
                    this.stompClient.subscribe('/user/queue/filter/result', (message) => {
                        this.handleRealtimeResult(JSON.parse(message.body));
                    });

                    this.stompClient.subscribe('/user/queue/crop/result', (message) => {
                        this.handleRealtimeResult(JSON.parse(message.body));
                    });

                    this.stompClient.subscribe('/user/queue/adjust/result', (message) => {
                        this.handleRealtimeResult(JSON.parse(message.body));
                    });

                    this.showNotification('WebSocket连接成功！', 'success');
                }, (error) => {
                    console.error('WebSocket连接失败: ' + error);
                    this.showNotification('WebSocket连接失败', 'error');
                });
            }

            handleFileSelect(event) {
                const files = event.target.files;
                if (files.length > 0) {
                    this.loadImageToCanvas(files[0]);
                }
            }

            handleDragOver(event) {
                event.preventDefault();
                event.currentTarget.style.borderColor = '#764ba2';
            }

            handleDrop(event) {
                event.preventDefault();
                event.currentTarget.style.borderColor = '#667eea';

                const files = event.dataTransfer.files;
                if (files.length > 0) {
                    this.loadImageToCanvas(files[0]);
                }
            }

            loadImageToCanvas(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        const originalCanvas = document.getElementById('originalCanvas');
                        const previewCanvas = document.getElementById('previewCanvas');

                        // 调整canvas大小
                        const maxWidth = 400;
                        const maxHeight = 300;
                        const ratio = Math.min(maxWidth / img.width, maxHeight / img.height);

                        originalCanvas.width = previewCanvas.width = img.width * ratio;
                        originalCanvas.height = previewCanvas.height = img.height * ratio;

                        // 绘制原图
                        const originalCtx = originalCanvas.getContext('2d');
                        originalCtx.drawImage(img, 0, 0, originalCanvas.width, originalCanvas.height);

                        // 绘制预览图
                        const previewCtx = previewCanvas.getContext('2d');
                        previewCtx.drawImage(img, 0, 0, previewCanvas.width, previewCanvas.height);

                        this.currentImage = img;
                        this.showNotification('图片加载成功！', 'success');
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }

            applyFilter(filterType) {
                if (!this.currentImage) {
                    this.showNotification('请先上传图片', 'warning');
                    return;
                }

                // 更新按钮状态
                document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
                document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

                this.currentFilter = filterType;
                this.showProcessing(true);

                // 发送实时处理请求
                const canvas = document.getElementById('originalCanvas');
                const imageData = canvas.toDataURL();

                const request = {
                    requestId: 'req_' + Date.now(),
                    imageData: imageData,
                    filterType: filterType,
                    processType: 'filter'
                };

                this.stompClient.send('/app/filter/preview', {}, JSON.stringify(request));
            }

            applyAdjustment() {
                if (!this.currentImage) return;

                const brightness = parseFloat(document.getElementById('brightnessSlider').value);
                const contrast = parseFloat(document.getElementById('contrastSlider').value);
                const saturation = parseFloat(document.getElementById('saturationSlider').value);

                this.showProcessing(true);

                const canvas = document.getElementById('originalCanvas');
                const imageData = canvas.toDataURL();

                const request = {
                    requestId: 'req_' + Date.now(),
                    imageData: imageData,
                    processType: 'adjust',
                    adjustParams: {
                        brightness: brightness,
                        contrast: contrast,
                        saturation: saturation
                    }
                };

                this.stompClient.send('/app/adjust/preview', {}, JSON.stringify(request));
            }

            handleRealtimeResult(result) {
                this.showProcessing(false);

                if (result.status === 'success' && result.previewImageData) {
                    const previewCanvas = document.getElementById('previewCanvas');
                    const ctx = previewCanvas.getContext('2d');

                    const img = new Image();
                    img.onload = () => {
                        ctx.clearRect(0, 0, previewCanvas.width, previewCanvas.height);
                        ctx.drawImage(img, 0, 0, previewCanvas.width, previewCanvas.height);
                    };
                    img.src = 'data:image/jpeg;base64,' + result.previewImageData;
                } else if (result.status === 'error') {
                    this.showNotification('处理失败: ' + result.message, 'error');
                }
            }

            showProcessing(show) {
                const overlay = document.getElementById('processingOverlay');
                overlay.style.display = show ? 'flex' : 'none';
            }

            showNotification(message, type = 'info') {
                const container = document.getElementById('notificationContainer');
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                `;

                container.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            loadGallery() {
                // 模拟加载图片库
                const galleryGrid = document.getElementById('galleryGrid');
                // 这里可以调用API加载真实的图片数据
            }

            // 其他方法的占位符
            enableCrop() { /* 实现裁剪功能 */ }
            applyCrop() { /* 应用裁剪 */ }
            cancelCrop() { /* 取消裁剪 */ }
            saveImage() { /* 保存图片 */ }
            resetImage() { /* 重置图片 */ }
            downloadImage() { /* 下载图片 */ }
            handleAIAnalysis(event) { /* AI分析 */ }
            switchTab(tabName) { /* 切换标签页 */ }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new PhotoMagicApp();
        });
    </script>
</body>
</html>
