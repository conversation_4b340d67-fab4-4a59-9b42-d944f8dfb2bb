package com.example.photoupload.controller;

import com.example.photoupload.dto.ImageSearchRequest;
import com.example.photoupload.dto.ImageSearchResponse;
import com.example.photoupload.service.ImageSearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 图像搜索控制器
 * 提供以图搜图、语义搜索等高级搜索功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/search")
@Tag(name = "图像搜索", description = "图像搜索引擎API")
public class ImageSearchController {

    private static final Logger logger = LoggerFactory.getLogger(ImageSearchController.class);

    @Autowired
    private ImageSearchService imageSearchService;

    /**
     * 以图搜图
     */
    @PostMapping("/by-image")
    @Operation(summary = "以图搜图", description = "上传图片进行相似图片搜索")
    public ResponseEntity<ImageSearchResponse> searchByImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(defaultValue = "0.7") double threshold) {
        try {
            logger.info("以图搜图: fileName={}, limit={}, threshold={}", 
                       file.getOriginalFilename(), limit, threshold);
            
            ImageSearchRequest request = new ImageSearchRequest();
            request.setSearchType("image");
            request.setLimit(limit);
            request.setSimilarityThreshold(threshold);
            
            ImageSearchResponse response = imageSearchService.searchByImage(file, request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("以图搜图失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("搜索失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 语义搜索
     */
    @PostMapping("/semantic")
    @Operation(summary = "语义搜索", description = "基于文本描述进行语义搜索")
    public ResponseEntity<ImageSearchResponse> semanticSearch(@RequestBody ImageSearchRequest request) {
        try {
            logger.info("语义搜索: query={}, limit={}", request.getQuery(), request.getLimit());
            
            ImageSearchResponse response = imageSearchService.semanticSearch(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("语义搜索失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("搜索失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 高级搜索
     */
    @PostMapping("/advanced")
    @Operation(summary = "高级搜索", description = "支持多种条件的高级搜索")
    public ResponseEntity<ImageSearchResponse> advancedSearch(@RequestBody ImageSearchRequest request) {
        try {
            logger.info("高级搜索: filters={}", request.getFilters());
            
            ImageSearchResponse response = imageSearchService.advancedSearch(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("高级搜索失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("搜索失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 颜色搜索
     */
    @GetMapping("/by-color")
    @Operation(summary = "颜色搜索", description = "根据主要颜色搜索图片")
    public ResponseEntity<ImageSearchResponse> searchByColor(
            @RequestParam String color,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(defaultValue = "0.8") double tolerance) {
        try {
            logger.info("颜色搜索: color={}, limit={}, tolerance={}", color, limit, tolerance);
            
            ImageSearchRequest request = new ImageSearchRequest();
            request.setSearchType("color");
            request.setQuery(color);
            request.setLimit(limit);
            request.setSimilarityThreshold(tolerance);
            
            ImageSearchResponse response = imageSearchService.searchByColor(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("颜色搜索失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("搜索失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 人脸搜索
     */
    @PostMapping("/by-face")
    @Operation(summary = "人脸搜索", description = "根据人脸特征搜索相似人物")
    public ResponseEntity<ImageSearchResponse> searchByFace(
            @RequestParam("file") MultipartFile file,
            @RequestParam(defaultValue = "20") int limit) {
        try {
            logger.info("人脸搜索: fileName={}, limit={}", file.getOriginalFilename(), limit);
            
            ImageSearchRequest request = new ImageSearchRequest();
            request.setSearchType("face");
            request.setLimit(limit);
            
            ImageSearchResponse response = imageSearchService.searchByFace(file, request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("人脸搜索失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("搜索失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 物体检测搜索
     */
    @GetMapping("/by-object")
    @Operation(summary = "物体搜索", description = "根据物体类型搜索图片")
    public ResponseEntity<ImageSearchResponse> searchByObject(
            @RequestParam String objectType,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(defaultValue = "0.7") double confidence) {
        try {
            logger.info("物体搜索: objectType={}, limit={}, confidence={}", objectType, limit, confidence);
            
            ImageSearchRequest request = new ImageSearchRequest();
            request.setSearchType("object");
            request.setQuery(objectType);
            request.setLimit(limit);
            request.setSimilarityThreshold(confidence);
            
            ImageSearchResponse response = imageSearchService.searchByObject(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("物体搜索失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("搜索失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 场景搜索
     */
    @GetMapping("/by-scene")
    @Operation(summary = "场景搜索", description = "根据场景类型搜索图片")
    public ResponseEntity<ImageSearchResponse> searchByScene(
            @RequestParam String sceneType,
            @RequestParam(defaultValue = "20") int limit) {
        try {
            logger.info("场景搜索: sceneType={}, limit={}", sceneType, limit);
            
            ImageSearchRequest request = new ImageSearchRequest();
            request.setSearchType("scene");
            request.setQuery(sceneType);
            request.setLimit(limit);
            
            ImageSearchResponse response = imageSearchService.searchByScene(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("场景搜索失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("搜索失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取搜索建议
     */
    @GetMapping("/suggestions")
    @Operation(summary = "搜索建议", description = "获取搜索关键词建议")
    public ResponseEntity<List<String>> getSearchSuggestions(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            logger.info("获取搜索建议: query={}, limit={}", query, limit);
            
            List<String> suggestions = imageSearchService.getSearchSuggestions(query, limit);
            
            return ResponseEntity.ok(suggestions);
            
        } catch (Exception e) {
            logger.error("获取搜索建议失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取热门搜索
     */
    @GetMapping("/trending")
    @Operation(summary = "热门搜索", description = "获取热门搜索关键词")
    public ResponseEntity<List<String>> getTrendingSearches(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            logger.info("获取热门搜索: limit={}", limit);
            
            List<String> trending = imageSearchService.getTrendingSearches(limit);
            
            return ResponseEntity.ok(trending);
            
        } catch (Exception e) {
            logger.error("获取热门搜索失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 搜索历史
     */
    @GetMapping("/history/{userId}")
    @Operation(summary = "搜索历史", description = "获取用户搜索历史")
    public ResponseEntity<List<ImageSearchResponse.SearchHistory>> getSearchHistory(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            logger.info("获取搜索历史: userId={}, page={}, size={}", userId, page, size);
            
            List<ImageSearchResponse.SearchHistory> history = imageSearchService.getSearchHistory(userId, page, size);
            
            return ResponseEntity.ok(history);
            
        } catch (Exception e) {
            logger.error("获取搜索历史失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 清除搜索历史
     */
    @DeleteMapping("/history/{userId}")
    @Operation(summary = "清除搜索历史", description = "清除用户搜索历史")
    public ResponseEntity<ImageSearchResponse> clearSearchHistory(@PathVariable String userId) {
        try {
            logger.info("清除搜索历史: userId={}", userId);
            
            ImageSearchResponse response = imageSearchService.clearSearchHistory(userId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("清除搜索历史失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("清除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 保存搜索
     */
    @PostMapping("/save")
    @Operation(summary = "保存搜索", description = "保存搜索条件以便后续使用")
    public ResponseEntity<ImageSearchResponse> saveSearch(@RequestBody ImageSearchRequest request) {
        try {
            logger.info("保存搜索: userId={}, name={}", request.getUserId(), request.getSearchName());
            
            ImageSearchResponse response = imageSearchService.saveSearch(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("保存搜索失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("保存失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取保存的搜索
     */
    @GetMapping("/saved/{userId}")
    @Operation(summary = "保存的搜索", description = "获取用户保存的搜索")
    public ResponseEntity<List<ImageSearchResponse.SavedSearch>> getSavedSearches(@PathVariable String userId) {
        try {
            logger.info("获取保存的搜索: userId={}", userId);
            
            List<ImageSearchResponse.SavedSearch> saved = imageSearchService.getSavedSearches(userId);
            
            return ResponseEntity.ok(saved);
            
        } catch (Exception e) {
            logger.error("获取保存的搜索失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 搜索统计
     */
    @GetMapping("/stats")
    @Operation(summary = "搜索统计", description = "获取搜索引擎统计信息")
    public ResponseEntity<ImageSearchResponse> getSearchStats() {
        try {
            logger.info("获取搜索统计");
            
            ImageSearchResponse response = imageSearchService.getSearchStats();
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取搜索统计失败", e);
            ImageSearchResponse errorResponse = ImageSearchResponse.error("获取统计失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
}
