package com.example.photoupload.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 图像搜索请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "图像搜索请求")
public class ImageSearchRequest {

    @Schema(description = "搜索类型", example = "image")
    private String searchType;

    @Schema(description = "搜索查询", example = "美丽的风景")
    private String query;

    @Schema(description = "用户ID", example = "user123")
    private String userId;

    @Schema(description = "返回结果数量限制", example = "20")
    private int limit = 20;

    @Schema(description = "相似度阈值", example = "0.7")
    private double similarityThreshold = 0.7;

    @Schema(description = "搜索过滤器")
    private SearchFilters filters;

    @Schema(description = "排序方式", example = "relevance")
    private String sortBy = "relevance";

    @Schema(description = "排序顺序", example = "desc")
    private String sortOrder = "desc";

    @Schema(description = "分页页码", example = "0")
    private int page = 0;

    @Schema(description = "分页大小", example = "20")
    private int size = 20;

    @Schema(description = "搜索名称（用于保存搜索）", example = "我的风景搜索")
    private String searchName;

    @Schema(description = "是否包含元数据", example = "true")
    private boolean includeMetadata = true;

    @Schema(description = "是否包含AI分析结果", example = "true")
    private boolean includeAiAnalysis = true;

    @Schema(description = "搜索范围")
    private SearchScope scope;

    @Schema(description = "搜索过滤器")
    public static class SearchFilters {
        @Schema(description = "文件类型", example = "jpg")
        private List<String> fileTypes;

        @Schema(description = "图片尺寸范围")
        private SizeRange sizeRange;

        @Schema(description = "文件大小范围")
        private SizeRange fileSizeRange;

        @Schema(description = "创建时间范围")
        private DateRange dateRange;

        @Schema(description = "颜色过滤器")
        private ColorFilter colorFilter;

        @Schema(description = "标签过滤器")
        private List<String> tags;

        @Schema(description = "用户过滤器")
        private List<String> users;

        @Schema(description = "相册过滤器")
        private List<String> albums;

        @Schema(description = "地理位置过滤器")
        private LocationFilter locationFilter;

        @Schema(description = "AI分析过滤器")
        private AiAnalysisFilter aiAnalysisFilter;

        // Getters and Setters
        public List<String> getFileTypes() { return fileTypes; }
        public void setFileTypes(List<String> fileTypes) { this.fileTypes = fileTypes; }

        public SizeRange getSizeRange() { return sizeRange; }
        public void setSizeRange(SizeRange sizeRange) { this.sizeRange = sizeRange; }

        public SizeRange getFileSizeRange() { return fileSizeRange; }
        public void setFileSizeRange(SizeRange fileSizeRange) { this.fileSizeRange = fileSizeRange; }

        public DateRange getDateRange() { return dateRange; }
        public void setDateRange(DateRange dateRange) { this.dateRange = dateRange; }

        public ColorFilter getColorFilter() { return colorFilter; }
        public void setColorFilter(ColorFilter colorFilter) { this.colorFilter = colorFilter; }

        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }

        public List<String> getUsers() { return users; }
        public void setUsers(List<String> users) { this.users = users; }

        public List<String> getAlbums() { return albums; }
        public void setAlbums(List<String> albums) { this.albums = albums; }

        public LocationFilter getLocationFilter() { return locationFilter; }
        public void setLocationFilter(LocationFilter locationFilter) { this.locationFilter = locationFilter; }

        public AiAnalysisFilter getAiAnalysisFilter() { return aiAnalysisFilter; }
        public void setAiAnalysisFilter(AiAnalysisFilter aiAnalysisFilter) { this.aiAnalysisFilter = aiAnalysisFilter; }
    }

    @Schema(description = "尺寸范围")
    public static class SizeRange {
        @Schema(description = "最小值", example = "100")
        private int min;

        @Schema(description = "最大值", example = "5000")
        private int max;

        public int getMin() { return min; }
        public void setMin(int min) { this.min = min; }

        public int getMax() { return max; }
        public void setMax(int max) { this.max = max; }
    }

    @Schema(description = "日期范围")
    public static class DateRange {
        @Schema(description = "开始日期")
        private LocalDateTime startDate;

        @Schema(description = "结束日期")
        private LocalDateTime endDate;

        public LocalDateTime getStartDate() { return startDate; }
        public void setStartDate(LocalDateTime startDate) { this.startDate = startDate; }

        public LocalDateTime getEndDate() { return endDate; }
        public void setEndDate(LocalDateTime endDate) { this.endDate = endDate; }
    }

    @Schema(description = "颜色过滤器")
    public static class ColorFilter {
        @Schema(description = "主要颜色", example = "red")
        private List<String> dominantColors;

        @Schema(description = "颜色容差", example = "0.1")
        private double tolerance = 0.1;

        @Schema(description = "颜色模式", example = "rgb")
        private String colorMode = "rgb";

        public List<String> getDominantColors() { return dominantColors; }
        public void setDominantColors(List<String> dominantColors) { this.dominantColors = dominantColors; }

        public double getTolerance() { return tolerance; }
        public void setTolerance(double tolerance) { this.tolerance = tolerance; }

        public String getColorMode() { return colorMode; }
        public void setColorMode(String colorMode) { this.colorMode = colorMode; }
    }

    @Schema(description = "地理位置过滤器")
    public static class LocationFilter {
        @Schema(description = "纬度", example = "39.9042")
        private double latitude;

        @Schema(description = "经度", example = "116.4074")
        private double longitude;

        @Schema(description = "半径（公里）", example = "10")
        private double radius;

        @Schema(description = "城市", example = "北京")
        private String city;

        @Schema(description = "国家", example = "中国")
        private String country;

        public double getLatitude() { return latitude; }
        public void setLatitude(double latitude) { this.latitude = latitude; }

        public double getLongitude() { return longitude; }
        public void setLongitude(double longitude) { this.longitude = longitude; }

        public double getRadius() { return radius; }
        public void setRadius(double radius) { this.radius = radius; }

        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }

        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
    }

    @Schema(description = "AI分析过滤器")
    public static class AiAnalysisFilter {
        @Schema(description = "检测到的物体")
        private List<String> detectedObjects;

        @Schema(description = "检测到的人物")
        private List<String> detectedPersons;

        @Schema(description = "场景类型")
        private List<String> sceneTypes;

        @Schema(description = "情感分析")
        private List<String> emotions;

        @Schema(description = "置信度阈值", example = "0.8")
        private double confidenceThreshold = 0.8;

        public List<String> getDetectedObjects() { return detectedObjects; }
        public void setDetectedObjects(List<String> detectedObjects) { this.detectedObjects = detectedObjects; }

        public List<String> getDetectedPersons() { return detectedPersons; }
        public void setDetectedPersons(List<String> detectedPersons) { this.detectedPersons = detectedPersons; }

        public List<String> getSceneTypes() { return sceneTypes; }
        public void setSceneTypes(List<String> sceneTypes) { this.sceneTypes = sceneTypes; }

        public List<String> getEmotions() { return emotions; }
        public void setEmotions(List<String> emotions) { this.emotions = emotions; }

        public double getConfidenceThreshold() { return confidenceThreshold; }
        public void setConfidenceThreshold(double confidenceThreshold) { this.confidenceThreshold = confidenceThreshold; }
    }

    @Schema(description = "搜索范围")
    public static class SearchScope {
        @Schema(description = "是否搜索公开图片", example = "true")
        private boolean includePublic = true;

        @Schema(description = "是否搜索私有图片", example = "false")
        private boolean includePrivate = false;

        @Schema(description = "是否搜索共享图片", example = "true")
        private boolean includeShared = true;

        @Schema(description = "特定用户范围")
        private List<String> userScope;

        @Schema(description = "特定相册范围")
        private List<String> albumScope;

        public boolean isIncludePublic() { return includePublic; }
        public void setIncludePublic(boolean includePublic) { this.includePublic = includePublic; }

        public boolean isIncludePrivate() { return includePrivate; }
        public void setIncludePrivate(boolean includePrivate) { this.includePrivate = includePrivate; }

        public boolean isIncludeShared() { return includeShared; }
        public void setIncludeShared(boolean includeShared) { this.includeShared = includeShared; }

        public List<String> getUserScope() { return userScope; }
        public void setUserScope(List<String> userScope) { this.userScope = userScope; }

        public List<String> getAlbumScope() { return albumScope; }
        public void setAlbumScope(List<String> albumScope) { this.albumScope = albumScope; }
    }

    // Main class getters and setters
    public String getSearchType() { return searchType; }
    public void setSearchType(String searchType) { this.searchType = searchType; }

    public String getQuery() { return query; }
    public void setQuery(String query) { this.query = query; }

    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }

    public int getLimit() { return limit; }
    public void setLimit(int limit) { this.limit = limit; }

    public double getSimilarityThreshold() { return similarityThreshold; }
    public void setSimilarityThreshold(double similarityThreshold) { this.similarityThreshold = similarityThreshold; }

    public SearchFilters getFilters() { return filters; }
    public void setFilters(SearchFilters filters) { this.filters = filters; }

    public String getSortBy() { return sortBy; }
    public void setSortBy(String sortBy) { this.sortBy = sortBy; }

    public String getSortOrder() { return sortOrder; }
    public void setSortOrder(String sortOrder) { this.sortOrder = sortOrder; }

    public int getPage() { return page; }
    public void setPage(int page) { this.page = page; }

    public int getSize() { return size; }
    public void setSize(int size) { this.size = size; }

    public String getSearchName() { return searchName; }
    public void setSearchName(String searchName) { this.searchName = searchName; }

    public boolean isIncludeMetadata() { return includeMetadata; }
    public void setIncludeMetadata(boolean includeMetadata) { this.includeMetadata = includeMetadata; }

    public boolean isIncludeAiAnalysis() { return includeAiAnalysis; }
    public void setIncludeAiAnalysis(boolean includeAiAnalysis) { this.includeAiAnalysis = includeAiAnalysis; }

    public SearchScope getScope() { return scope; }
    public void setScope(SearchScope scope) { this.scope = scope; }
}
