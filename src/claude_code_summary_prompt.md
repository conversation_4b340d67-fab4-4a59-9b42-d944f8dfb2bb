Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
<!-- 你的任务是创建到目前为止对话的详细摘要，密切关注用户的明确请求和你之前的操作。 -->
This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing development work without losing context.
<!-- 这个摘要应该全面捕获技术细节、代码模式和架构决策，这些对于继续开发工作而不丢失上下文是必要的。 -->
Before providing your final summary, wrap your analysis in <analysis> tags to organize your thoughts and ensure you've covered all necessary points. In your analysis process:
<!-- 在提供最终摘要之前，将你的分析包装在<analysis>标签中以组织你的思路并确保你已经覆盖了所有必要的要点。在你的分析过程中： -->
1. Chronologically analyze each message and section of the conversation. For each section thoroughly identify:
<!-- 1. 按时间顺序分析对话的每条消息和每个部分。对于每个部分，彻底识别： -->
   - The user's explicit requests and intents
   <!-- - 用户的明确请求和意图 -->
   - Your approach to addressing the user's requests
   <!-- - 你处理用户请求的方法 -->
   - Key decisions, technical concepts and code patterns
   <!-- - 关键决策、技术概念和代码模式 -->
   - Specific details like:
   <!-- - 具体细节，如： -->
     - file names
     <!-- - 文件名 -->
     - full code snippets
     <!-- - 完整的代码片段 -->
     - function signatures
     <!-- - 函数签名 -->
     - file edits
     <!-- - 文件编辑 -->
  - Errors that you ran into and how you fixed them
  <!-- - 你遇到的错误以及如何修复它们 -->
  - Pay special attention to specific user feedback that you received, especially if the user told you to do something differently.
  <!-- - 特别注意你收到的具体用户反馈，特别是如果用户告诉你要做得不同。 -->
  2. Double-check for technical accuracy and completeness, addressing each required element thoroughly.
  <!-- 2. 仔细检查技术准确性和完整性，彻底处理每个必需的元素。 -->
Your summary should include the following sections:
<!-- 你的摘要应该包括以下部分： -->
1. Primary Request and Intent: Capture all of the user's explicit requests and intents in detail
<!-- 1. 主要请求和意图：详细捕获用户的所有明确请求和意图 -->
2. Key Technical Concepts: List all important technical concepts, technologies, and frameworks discussed.
<!-- 2. 关键技术概念：列出所有讨论过的重要技术概念、技术和框架。 -->
3. Files and Code Sections: Enumerate specific files and code sections examined, modified, or created. Pay special attention to the most recent messages and include full code snippets where applicable and include a summary of why this file read or edit is important.
<!-- 3. 文件和代码部分：枚举检查、修改或创建的具体文件和代码部分。特别注意最近的消息，并在适用时包含完整的代码片段，并包含为什么这个文件读取或编辑很重要的摘要。 -->
4. Errors and fixes: List all errors that you ran into, and how you fixed them. Pay special attention to specific user feedback that you received, especially if the user told you to do something differently.
<!-- 4. 错误和修复：列出你遇到的所有错误，以及如何修复它们。特别注意你收到的具体用户反馈，特别是如果用户告诉你要做得不同。 -->
5. Problem Solving: Document problems solved and any ongoing troubleshooting efforts.
<!-- 5. 问题解决：记录已解决的问题和任何正在进行的故障排除工作。 -->
6. All user messages: List ALL user messages that are not tool results. These are critical for understanding the users' feedback and changing intent.
<!-- 6. 所有用户消息：列出所有不是工具结果的用户消息。这些对于理解用户的反馈和变化的意图至关重要。 -->
6. Pending Tasks: Outline any pending tasks that you have explicitly been asked to work on.
<!-- 6. 待处理任务：概述你被明确要求处理的任何待处理任务。 -->
7. Current Work: Describe in detail precisely what was being worked on immediately before this summary request, paying special attention to the most recent messages from both user and assistant. Include file names and code snippets where applicable.
<!-- 7. 当前工作：详细描述在此摘要请求之前正在进行的确切工作，特别注意用户和助手的最新消息。在适用时包含文件名和代码片段。 -->
8. Optional Next Step: List the next step that you will take that is related to the most recent work you were doing. IMPORTANT: ensure that this step is DIRECTLY in line with the user's explicit requests, and the task you were working on immediately before this summary request. If your last task was concluded, then only list next steps if they are explicitly in line with the users request. Do not start on tangential requests without confirming with the user first.
<!-- 8. 可选的下一步：列出与你最近正在做的工作相关的下一步。重要：确保这一步直接符合用户的明确请求，以及你在此摘要请求之前立即进行的任务。如果你的最后一个任务已经结束，那么只有在明确符合用户请求的情况下才列出下一步。不要在未首先与用户确认的情况下开始切线请求。 -->
If there is a next step, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no drift in task interpretation.
<!-- 如果有下一步，请包括最近对话的直接引用，显示你正在进行的确切任务以及你停止的地方。这应该是逐字的，以确保任务解释没有偏差。 -->
Here's an example of how your output should be structured:
<!-- 以下是你的输出应该如何结构化的示例： -->
<example>
<!-- <example> 示例标签开始 -->
<analysis>
<!-- <analysis> 分析标签开始 -->
[Your thought process, ensuring all points are covered thoroughly and accurately]
<!-- [你的思考过程，确保所有要点都得到彻底和准确的覆盖] -->
</analysis>
<!-- </analysis> 分析标签结束 -->
<summary>
<!-- <summary> 摘要标签开始 -->
1. Primary Request and Intent:
<!-- 1. 主要请求和意图： -->
   [Detailed description]
   <!-- [详细描述] -->
   2. Key Technical Concepts:
   <!-- 2. 关键技术概念： -->
   - [Concept 1]
   <!-- - [概念1] -->
   - [Concept 2]
   <!-- - [概念2] -->
   - [...]
   <!-- - [...] -->
   3. Files and Code Sections:
   <!-- 3. 文件和代码部分： -->
   - [File Name 1]
   <!-- - [文件名1] -->
      - [Summary of why this file is important]
      <!-- - [为什么这个文件很重要的摘要] -->
      - [Summary of the changes made to this file, if any]
      <!-- - [对此文件所做更改的摘要（如果有的话）] -->
      - [Important Code Snippet]
      <!-- - [重要代码片段] -->
   - [File Name 2]
   <!-- - [文件名2] -->
      - [Important Code Snippet]
      <!-- - [重要代码片段] -->
   - [...]
   <!-- - [...] -->
   4. Errors and fixes:
   <!-- 4. 错误和修复： -->
    - [Detailed description of error 1]:
    <!-- - [错误1的详细描述]： -->
      - [How you fixed the error]
      <!-- - [你如何修复错误] -->
      - [User feedback on the error if any]
      <!-- - [用户对错误的反馈（如果有的话）] -->
    - [...]
    <!-- - [...] -->
    5. Problem Solving:
    <!-- 5. 问题解决： -->
   [Description of solved problems and ongoing troubleshooting]
   <!-- [已解决问题和正在进行的故障排除的描述] -->
   6. All user messages: 
   <!-- 6. 所有用户消息： -->
    - [Detailed non tool use user message]
    <!-- - [详细的非工具使用用户消息] -->
    - [...]
    <!-- - [...] -->
    7. Pending Tasks:
    <!-- 7. 待处理任务： -->
   - [Task 1]
   <!-- - [任务1] -->
   - [Task 2]
   <!-- - [任务2] -->
   - [...]
   <!-- - [...] -->
   8. Current Work:
   <!-- 8. 当前工作： -->
   [Precise description of current work]
   <!-- [当前工作的精确描述] -->
   9. Optional Next Step:
   <!-- 9. 可选的下一步： -->
   [Optional Next step to take]
   <!-- [可选的下一步行动] -->
   </summary>
   <!-- </summary> 摘要标签结束 -->
</example>
<!-- </example> 示例标签结束 -->
Please provide your summary based on the conversation so far, following this structure and ensuring precision and thoroughness in your response. 
<!-- 请根据到目前为止的对话提供你的摘要，遵循这个结构并确保你的回应的精确性和彻底性。 -->
There may be additional summarization instructions provided in the included context. If so, remember to follow these instructions when creating the above summary. Examples of instructions include:
<!-- 在包含的上下文中可能提供额外的总结说明。如果是这样，请记住在创建上述摘要时遵循这些说明。说明示例包括： -->
<example>
<!-- <example> 示例标签开始 -->
## Compact Instructions
<!-- ## 紧凑说明 -->
When summarizing the conversation focus on typescript code changes and also remember the mistakes you made and how you fixed them.
<!-- 在总结对话时，重点关注typescript代码更改，并记住你犯的错误以及如何修复它们。 -->
</example>
<!-- </example> 示例标签结束 -->
<example>
<!-- <example> 示例标签开始 -->
# Summary instructions
<!-- # 摘要说明 -->
When you are using compact - please focus on test output and code changes. Include file reads verbatim.
<!-- 当你使用紧凑模式时 - 请专注于测试输出和代码更改。逐字包含文件读取。 -->
</example>
<!-- </example> 示例标签结束 -->