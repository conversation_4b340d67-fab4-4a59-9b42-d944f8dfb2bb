package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * 实时处理请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "实时处理请求")
public class RealtimeProcessRequest {

    @Schema(description = "请求ID", example = "req_123456")
    @JsonProperty("requestId")
    private String requestId;

    @Schema(description = "图像文件名", example = "image.jpg")
    @JsonProperty("fileName")
    private String fileName;

    @Schema(description = "图像Base64数据")
    @JsonProperty("imageData")
    private String imageData;

    @Schema(description = "处理类型", example = "filter")
    @JsonProperty("processType")
    private String processType;

    @Schema(description = "滤镜类型", example = "vintage")
    @JsonProperty("filterType")
    private String filterType;

    @Schema(description = "裁剪参数")
    @JsonProperty("cropParams")
    private CropParams cropParams;

    @Schema(description = "调色参数")
    @JsonProperty("adjustParams")
    private AdjustParams adjustParams;

    @Schema(description = "其他参数")
    @JsonProperty("extraParams")
    private Map<String, Object> extraParams;

    @Schema(description = "预览质量", example = "medium")
    @JsonProperty("previewQuality")
    private String previewQuality = "medium";

    // 构造函数
    public RealtimeProcessRequest() {}

    // Getters and Setters
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getImageData() {
        return imageData;
    }

    public void setImageData(String imageData) {
        this.imageData = imageData;
    }

    public String getProcessType() {
        return processType;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public String getFilterType() {
        return filterType;
    }

    public void setFilterType(String filterType) {
        this.filterType = filterType;
    }

    public CropParams getCropParams() {
        return cropParams;
    }

    public void setCropParams(CropParams cropParams) {
        this.cropParams = cropParams;
    }

    public AdjustParams getAdjustParams() {
        return adjustParams;
    }

    public void setAdjustParams(AdjustParams adjustParams) {
        this.adjustParams = adjustParams;
    }

    public Map<String, Object> getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(Map<String, Object> extraParams) {
        this.extraParams = extraParams;
    }

    public String getPreviewQuality() {
        return previewQuality;
    }

    public void setPreviewQuality(String previewQuality) {
        this.previewQuality = previewQuality;
    }

    /**
     * 裁剪参数内部类
     */
    @Schema(description = "裁剪参数")
    public static class CropParams {
        @Schema(description = "X坐标", example = "100")
        private int x;

        @Schema(description = "Y坐标", example = "100")
        private int y;

        @Schema(description = "宽度", example = "300")
        private int width;

        @Schema(description = "高度", example = "300")
        private int height;

        // 构造函数
        public CropParams() {}

        public CropParams(int x, int y, int width, int height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }

        // Getters and Setters
        public int getX() { return x; }
        public void setX(int x) { this.x = x; }

        public int getY() { return y; }
        public void setY(int y) { this.y = y; }

        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }

        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
    }

    /**
     * 调色参数内部类
     */
    @Schema(description = "调色参数")
    public static class AdjustParams {
        @Schema(description = "亮度", example = "0.1")
        private float brightness = 0.0f;

        @Schema(description = "对比度", example = "0.1")
        private float contrast = 0.0f;

        @Schema(description = "饱和度", example = "0.1")
        private float saturation = 0.0f;

        @Schema(description = "色调", example = "0.1")
        private float hue = 0.0f;

        @Schema(description = "伽马值", example = "1.0")
        private float gamma = 1.0f;

        // 构造函数
        public AdjustParams() {}

        // Getters and Setters
        public float getBrightness() { return brightness; }
        public void setBrightness(float brightness) { this.brightness = brightness; }

        public float getContrast() { return contrast; }
        public void setContrast(float contrast) { this.contrast = contrast; }

        public float getSaturation() { return saturation; }
        public void setSaturation(float saturation) { this.saturation = saturation; }

        public float getHue() { return hue; }
        public void setHue(float hue) { this.hue = hue; }

        public float getGamma() { return gamma; }
        public void setGamma(float gamma) { this.gamma = gamma; }
    }
}
