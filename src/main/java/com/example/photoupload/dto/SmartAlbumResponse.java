package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 智能相册响应DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "智能相册响应")
public class SmartAlbumResponse {

    @Schema(description = "操作是否成功", example = "true")
    @JsonProperty("success")
    private boolean success;

    @Schema(description = "响应消息", example = "智能相册创建成功")
    @JsonProperty("message")
    private String message;

    @Schema(description = "相册ID", example = "album_123456")
    @JsonProperty("albumId")
    private String albumId;

    @Schema(description = "相册名称", example = "我的家人")
    @JsonProperty("albumName")
    private String albumName;

    @Schema(description = "相册类型", example = "person")
    @JsonProperty("albumType")
    private String albumType;

    @Schema(description = "相册描述")
    @JsonProperty("description")
    private String description;

    @Schema(description = "图片列表")
    @JsonProperty("images")
    private List<AlbumImage> images;

    @Schema(description = "图片总数", example = "25")
    @JsonProperty("totalImages")
    private int totalImages;

    @Schema(description = "创建时间")
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;

    @Schema(description = "标签列表")
    @JsonProperty("tags")
    private List<String> tags;

    @Schema(description = "相册统计信息")
    @JsonProperty("statistics")
    private AlbumStatistics statistics;

    @Schema(description = "相册设置")
    @JsonProperty("settings")
    private Map<String, Object> settings;

    @Schema(description = "缩略图URL")
    @JsonProperty("thumbnailUrl")
    private String thumbnailUrl;

    @Schema(description = "是否自动更新", example = "true")
    @JsonProperty("autoUpdate")
    private boolean autoUpdate;

    @Schema(description = "隐私设置", example = "private")
    @JsonProperty("privacy")
    private String privacy;

    @Schema(description = "处理时间(毫秒)", example = "1500")
    @JsonProperty("processingTime")
    private long processingTime;

    // 构造函数
    public SmartAlbumResponse() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // 静态工厂方法
    public static SmartAlbumResponse success(String albumId, String message) {
        SmartAlbumResponse response = new SmartAlbumResponse();
        response.setSuccess(true);
        response.setAlbumId(albumId);
        response.setMessage(message);
        return response;
    }

    public static SmartAlbumResponse error(String message) {
        SmartAlbumResponse response = new SmartAlbumResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getAlbumId() {
        return albumId;
    }

    public void setAlbumId(String albumId) {
        this.albumId = albumId;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public String getAlbumType() {
        return albumType;
    }

    public void setAlbumType(String albumType) {
        this.albumType = albumType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<AlbumImage> getImages() {
        return images;
    }

    public void setImages(List<AlbumImage> images) {
        this.images = images;
    }

    public int getTotalImages() {
        return totalImages;
    }

    public void setTotalImages(int totalImages) {
        this.totalImages = totalImages;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public AlbumStatistics getStatistics() {
        return statistics;
    }

    public void setStatistics(AlbumStatistics statistics) {
        this.statistics = statistics;
    }

    public Map<String, Object> getSettings() {
        return settings;
    }

    public void setSettings(Map<String, Object> settings) {
        this.settings = settings;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public boolean isAutoUpdate() {
        return autoUpdate;
    }

    public void setAutoUpdate(boolean autoUpdate) {
        this.autoUpdate = autoUpdate;
    }

    public String getPrivacy() {
        return privacy;
    }

    public void setPrivacy(String privacy) {
        this.privacy = privacy;
    }

    public long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(long processingTime) {
        this.processingTime = processingTime;
    }

    /**
     * 相册图片内部类
     */
    @Schema(description = "相册图片信息")
    public static class AlbumImage {
        @Schema(description = "图片ID")
        private String imageId;

        @Schema(description = "文件名")
        private String fileName;

        @Schema(description = "图片URL")
        private String imageUrl;

        @Schema(description = "缩略图URL")
        private String thumbnailUrl;

        @Schema(description = "匹配分数", example = "0.95")
        private float matchScore;

        @Schema(description = "置信度", example = "0.88")
        private float confidence;

        @Schema(description = "匹配原因")
        private List<String> matchReasons;

        @Schema(description = "AI分析结果")
        private Map<String, Object> aiAnalysis;

        @Schema(description = "添加时间")
        private LocalDateTime addedAt;

        // Getters and Setters
        public String getImageId() { return imageId; }
        public void setImageId(String imageId) { this.imageId = imageId; }

        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }

        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }

        public float getMatchScore() { return matchScore; }
        public void setMatchScore(float matchScore) { this.matchScore = matchScore; }

        public float getConfidence() { return confidence; }
        public void setConfidence(float confidence) { this.confidence = confidence; }

        public List<String> getMatchReasons() { return matchReasons; }
        public void setMatchReasons(List<String> matchReasons) { this.matchReasons = matchReasons; }

        public Map<String, Object> getAiAnalysis() { return aiAnalysis; }
        public void setAiAnalysis(Map<String, Object> aiAnalysis) { this.aiAnalysis = aiAnalysis; }

        public LocalDateTime getAddedAt() { return addedAt; }
        public void setAddedAt(LocalDateTime addedAt) { this.addedAt = addedAt; }
    }

    /**
     * 相册统计信息内部类
     */
    @Schema(description = "相册统计信息")
    public static class AlbumStatistics {
        @Schema(description = "总图片数", example = "150")
        private int totalImages;

        @Schema(description = "人物数量", example = "5")
        private int personCount;

        @Schema(description = "场景类型数量", example = "8")
        private int sceneTypeCount;

        @Schema(description = "平均匹配分数", example = "0.85")
        private float averageMatchScore;

        @Schema(description = "最新添加时间")
        private LocalDateTime lastAddedAt;

        @Schema(description = "文件大小总计(字节)", example = "52428800")
        private long totalFileSize;

        @Schema(description = "按日期分组的统计")
        private Map<String, Integer> dateDistribution;

        @Schema(description = "按标签分组的统计")
        private Map<String, Integer> tagDistribution;

        @Schema(description = "质量分布")
        private Map<String, Integer> qualityDistribution;

        // Getters and Setters
        public int getTotalImages() { return totalImages; }
        public void setTotalImages(int totalImages) { this.totalImages = totalImages; }

        public int getPersonCount() { return personCount; }
        public void setPersonCount(int personCount) { this.personCount = personCount; }

        public int getSceneTypeCount() { return sceneTypeCount; }
        public void setSceneTypeCount(int sceneTypeCount) { this.sceneTypeCount = sceneTypeCount; }

        public float getAverageMatchScore() { return averageMatchScore; }
        public void setAverageMatchScore(float averageMatchScore) { this.averageMatchScore = averageMatchScore; }

        public LocalDateTime getLastAddedAt() { return lastAddedAt; }
        public void setLastAddedAt(LocalDateTime lastAddedAt) { this.lastAddedAt = lastAddedAt; }

        public long getTotalFileSize() { return totalFileSize; }
        public void setTotalFileSize(long totalFileSize) { this.totalFileSize = totalFileSize; }

        public Map<String, Integer> getDateDistribution() { return dateDistribution; }
        public void setDateDistribution(Map<String, Integer> dateDistribution) { this.dateDistribution = dateDistribution; }

        public Map<String, Integer> getTagDistribution() { return tagDistribution; }
        public void setTagDistribution(Map<String, Integer> tagDistribution) { this.tagDistribution = tagDistribution; }

        public Map<String, Integer> getQualityDistribution() { return qualityDistribution; }
        public void setQualityDistribution(Map<String, Integer> qualityDistribution) { this.qualityDistribution = qualityDistribution; }
    }
}
