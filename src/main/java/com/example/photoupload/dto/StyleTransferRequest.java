package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * 风格迁移请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "风格迁移请求")
public class StyleTransferRequest {

    @Schema(description = "内容图片路径", example = "uploads/content.jpg")
    @JsonProperty("contentImagePath")
    private String contentImagePath;

    @Schema(description = "风格类型", example = "van_gogh")
    @JsonProperty("styleType")
    private String styleType;

    @Schema(description = "自定义风格图片路径", example = "uploads/custom_style.jpg")
    @JsonProperty("customStylePath")
    private String customStylePath;

    @Schema(description = "风格强度", example = "0.8")
    @JsonProperty("styleStrength")
    private float styleStrength = 0.8f;

    @Schema(description = "输出质量", example = "high")
    @JsonProperty("outputQuality")
    private String outputQuality = "high";

    @Schema(description = "输出尺寸", example = "1024x768")
    @JsonProperty("outputSize")
    private String outputSize;

    @Schema(description = "保持内容结构", example = "true")
    @JsonProperty("preserveContent")
    private boolean preserveContent = true;

    @Schema(description = "颜色保持程度", example = "0.3")
    @JsonProperty("colorPreservation")
    private float colorPreservation = 0.3f;

    @Schema(description = "处理优先级", example = "normal")
    @JsonProperty("priority")
    private String priority = "normal";

    @Schema(description = "是否异步处理", example = "false")
    @JsonProperty("async")
    private boolean async = false;

    @Schema(description = "通知回调URL")
    @JsonProperty("callbackUrl")
    private String callbackUrl;

    @Schema(description = "额外参数")
    @JsonProperty("parameters")
    private StyleParameters parameters;

    @Schema(description = "输出格式", example = "jpg")
    @JsonProperty("outputFormat")
    private String outputFormat = "jpg";

    @Schema(description = "压缩质量", example = "90")
    @JsonProperty("compressionQuality")
    private int compressionQuality = 90;

    @Schema(description = "水印设置")
    @JsonProperty("watermark")
    private WatermarkSettings watermark;

    @Schema(description = "元数据保留", example = "false")
    @JsonProperty("preserveMetadata")
    private boolean preserveMetadata = false;

    // 构造函数
    public StyleTransferRequest() {
        this.parameters = new StyleParameters();
    }

    // Getters and Setters
    public String getContentImagePath() {
        return contentImagePath;
    }

    public void setContentImagePath(String contentImagePath) {
        this.contentImagePath = contentImagePath;
    }

    public String getStyleType() {
        return styleType;
    }

    public void setStyleType(String styleType) {
        this.styleType = styleType;
    }

    public String getCustomStylePath() {
        return customStylePath;
    }

    public void setCustomStylePath(String customStylePath) {
        this.customStylePath = customStylePath;
    }

    public float getStyleStrength() {
        return styleStrength;
    }

    public void setStyleStrength(float styleStrength) {
        this.styleStrength = styleStrength;
    }

    public String getOutputQuality() {
        return outputQuality;
    }

    public void setOutputQuality(String outputQuality) {
        this.outputQuality = outputQuality;
    }

    public String getOutputSize() {
        return outputSize;
    }

    public void setOutputSize(String outputSize) {
        this.outputSize = outputSize;
    }

    public boolean isPreserveContent() {
        return preserveContent;
    }

    public void setPreserveContent(boolean preserveContent) {
        this.preserveContent = preserveContent;
    }

    public float getColorPreservation() {
        return colorPreservation;
    }

    public void setColorPreservation(float colorPreservation) {
        this.colorPreservation = colorPreservation;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public boolean isAsync() {
        return async;
    }

    public void setAsync(boolean async) {
        this.async = async;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public StyleParameters getParameters() {
        return parameters;
    }

    public void setParameters(StyleParameters parameters) {
        this.parameters = parameters;
    }

    public String getOutputFormat() {
        return outputFormat;
    }

    public void setOutputFormat(String outputFormat) {
        this.outputFormat = outputFormat;
    }

    public int getCompressionQuality() {
        return compressionQuality;
    }

    public void setCompressionQuality(int compressionQuality) {
        this.compressionQuality = compressionQuality;
    }

    public WatermarkSettings getWatermark() {
        return watermark;
    }

    public void setWatermark(WatermarkSettings watermark) {
        this.watermark = watermark;
    }

    public boolean isPreserveMetadata() {
        return preserveMetadata;
    }

    public void setPreserveMetadata(boolean preserveMetadata) {
        this.preserveMetadata = preserveMetadata;
    }

    /**
     * 风格参数内部类
     */
    @Schema(description = "风格参数")
    public static class StyleParameters {
        @Schema(description = "内容权重", example = "1.0")
        private float contentWeight = 1.0f;

        @Schema(description = "风格权重", example = "100.0")
        private float styleWeight = 100.0f;

        @Schema(description = "总变差权重", example = "1.0")
        private float totalVariationWeight = 1.0f;

        @Schema(description = "迭代次数", example = "500")
        private int iterations = 500;

        @Schema(description = "学习率", example = "0.02")
        private float learningRate = 0.02f;

        @Schema(description = "风格层级")
        private String[] styleLayers = {"conv1_1", "conv2_1", "conv3_1", "conv4_1", "conv5_1"};

        @Schema(description = "内容层级")
        private String[] contentLayers = {"conv4_2"};

        @Schema(description = "优化器类型", example = "adam")
        private String optimizer = "adam";

        @Schema(description = "批处理大小", example = "1")
        private int batchSize = 1;

        @Schema(description = "GPU使用", example = "true")
        private boolean useGpu = true;

        @Schema(description = "混合精度", example = "false")
        private boolean mixedPrecision = false;

        @Schema(description = "额外设置")
        private Map<String, Object> additionalSettings;

        // Getters and Setters
        public float getContentWeight() { return contentWeight; }
        public void setContentWeight(float contentWeight) { this.contentWeight = contentWeight; }

        public float getStyleWeight() { return styleWeight; }
        public void setStyleWeight(float styleWeight) { this.styleWeight = styleWeight; }

        public float getTotalVariationWeight() { return totalVariationWeight; }
        public void setTotalVariationWeight(float totalVariationWeight) { this.totalVariationWeight = totalVariationWeight; }

        public int getIterations() { return iterations; }
        public void setIterations(int iterations) { this.iterations = iterations; }

        public float getLearningRate() { return learningRate; }
        public void setLearningRate(float learningRate) { this.learningRate = learningRate; }

        public String[] getStyleLayers() { return styleLayers; }
        public void setStyleLayers(String[] styleLayers) { this.styleLayers = styleLayers; }

        public String[] getContentLayers() { return contentLayers; }
        public void setContentLayers(String[] contentLayers) { this.contentLayers = contentLayers; }

        public String getOptimizer() { return optimizer; }
        public void setOptimizer(String optimizer) { this.optimizer = optimizer; }

        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }

        public boolean isUseGpu() { return useGpu; }
        public void setUseGpu(boolean useGpu) { this.useGpu = useGpu; }

        public boolean isMixedPrecision() { return mixedPrecision; }
        public void setMixedPrecision(boolean mixedPrecision) { this.mixedPrecision = mixedPrecision; }

        public Map<String, Object> getAdditionalSettings() { return additionalSettings; }
        public void setAdditionalSettings(Map<String, Object> additionalSettings) { this.additionalSettings = additionalSettings; }
    }

    /**
     * 水印设置内部类
     */
    @Schema(description = "水印设置")
    public static class WatermarkSettings {
        @Schema(description = "是否启用水印", example = "false")
        private boolean enabled = false;

        @Schema(description = "水印文本", example = "PhotoMagic")
        private String text;

        @Schema(description = "水印位置", example = "bottom_right")
        private String position = "bottom_right";

        @Schema(description = "水印透明度", example = "0.5")
        private float opacity = 0.5f;

        @Schema(description = "水印大小", example = "12")
        private int fontSize = 12;

        @Schema(description = "水印颜色", example = "#FFFFFF")
        private String color = "#FFFFFF";

        @Schema(description = "水印字体", example = "Arial")
        private String fontFamily = "Arial";

        @Schema(description = "边距", example = "10")
        private int margin = 10;

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public String getText() { return text; }
        public void setText(String text) { this.text = text; }

        public String getPosition() { return position; }
        public void setPosition(String position) { this.position = position; }

        public float getOpacity() { return opacity; }
        public void setOpacity(float opacity) { this.opacity = opacity; }

        public int getFontSize() { return fontSize; }
        public void setFontSize(int fontSize) { this.fontSize = fontSize; }

        public String getColor() { return color; }
        public void setColor(String color) { this.color = color; }

        public String getFontFamily() { return fontFamily; }
        public void setFontFamily(String fontFamily) { this.fontFamily = fontFamily; }

        public int getMargin() { return margin; }
        public void setMargin(int margin) { this.margin = margin; }
    }
}
