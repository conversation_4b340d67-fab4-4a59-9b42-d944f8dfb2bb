package com.example.photoupload.service.impl;

import com.example.photoupload.dto.SocialRequest;
import com.example.photoupload.dto.SocialResponse;
import com.example.photoupload.service.SocialService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 社交功能服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class SocialServiceImpl implements SocialService {

    private static final Logger logger = LoggerFactory.getLogger(SocialServiceImpl.class);

    // 内存存储（实际项目中应使用数据库）
    private final Map<String, Set<String>> imageLikes = new ConcurrentHashMap<>();
    private final Map<String, List<SocialResponse.Comment>> imageComments = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> userFollows = new ConcurrentHashMap<>();
    private final Map<String, SocialResponse.UserStats> userStats = new ConcurrentHashMap<>();
    private final Map<String, List<SocialResponse.FeedItem>> userFeeds = new ConcurrentHashMap<>();
    private final Map<String, String> shareLinks = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> blockedUsers = new ConcurrentHashMap<>();
    private final Map<String, List<String>> notifications = new ConcurrentHashMap<>();
    private final Set<String> trendingHashtags = new HashSet<>();

    @Override
    public SocialResponse shareImage(SocialRequest request) {
        try {
            logger.info("分享图片: imageId={}, platform={}", request.getImageId(), request.getPlatform());
            
            // 验证请求
            if (request.getImageId() == null || request.getPlatform() == null) {
                return SocialResponse.error("参数不能为空");
            }
            
            // 生成分享ID
            String shareId = "share_" + UUID.randomUUID().toString().replace("-", "");
            
            // 生成分享链接
            String shareUrl = generateShareUrl(request.getImageId(), request.getPlatform());
            shareLinks.put(shareId, shareUrl);
            
            // 更新分享统计
            updateShareStats(request.getImageId(), request.getUserId());
            
            // 创建动态
            createFeedItem(request.getUserId(), "share", request.getImageId(), 
                          "分享了图片到" + getPlatformName(request.getPlatform()));
            
            SocialResponse response = SocialResponse.success("分享成功");
            response.setShareId(shareId);
            response.setShareUrl(shareUrl);
            response.setShareCount(getShareCount(request.getImageId()));
            
            return response;
            
        } catch (Exception e) {
            logger.error("分享图片失败", e);
            return SocialResponse.error("分享失败: " + e.getMessage());
        }
    }

    @Override
    public SocialResponse likeImage(String imageId, String userId) {
        try {
            logger.info("点赞图片: imageId={}, userId={}", imageId, userId);
            
            Set<String> likes = imageLikes.computeIfAbsent(imageId, k -> new HashSet<>());
            
            if (likes.contains(userId)) {
                return SocialResponse.error("已经点过赞了");
            }
            
            likes.add(userId);
            
            // 更新用户统计
            updateUserStats(userId, "like", 1);
            
            // 创建动态
            createFeedItem(userId, "like", imageId, "点赞了图片");
            
            // 发送通知给图片作者
            sendNotification(getImageOwner(imageId), userId + " 点赞了你的图片");
            
            SocialResponse response = SocialResponse.success("点赞成功");
            response.setLikeCount(likes.size());
            response.setLiked(true);
            
            return response;
            
        } catch (Exception e) {
            logger.error("点赞失败", e);
            return SocialResponse.error("点赞失败: " + e.getMessage());
        }
    }

    @Override
    public SocialResponse unlikeImage(String imageId, String userId) {
        try {
            logger.info("取消点赞: imageId={}, userId={}", imageId, userId);
            
            Set<String> likes = imageLikes.get(imageId);
            if (likes == null || !likes.contains(userId)) {
                return SocialResponse.error("还没有点赞");
            }
            
            likes.remove(userId);
            
            // 更新用户统计
            updateUserStats(userId, "like", -1);
            
            SocialResponse response = SocialResponse.success("取消点赞成功");
            response.setLikeCount(likes.size());
            response.setLiked(false);
            
            return response;
            
        } catch (Exception e) {
            logger.error("取消点赞失败", e);
            return SocialResponse.error("取消点赞失败: " + e.getMessage());
        }
    }

    @Override
    public SocialResponse addComment(SocialRequest request) {
        try {
            logger.info("添加评论: imageId={}, userId={}", request.getImageId(), request.getUserId());
            
            if (request.getComment() == null || request.getComment().trim().isEmpty()) {
                return SocialResponse.error("评论内容不能为空");
            }
            
            // 创建评论
            SocialResponse.Comment comment = new SocialResponse.Comment();
            comment.setCommentId("comment_" + UUID.randomUUID().toString().replace("-", ""));
            comment.setUserId(request.getUserId());
            comment.setUsername(getUserName(request.getUserId()));
            comment.setAvatar(getUserAvatar(request.getUserId()));
            comment.setContent(request.getComment());
            comment.setLikeCount(0);
            comment.setLiked(false);
            comment.setReplyCount(0);
            comment.setCreatedAt(LocalDateTime.now());
            
            // 存储评论
            List<SocialResponse.Comment> comments = imageComments.computeIfAbsent(request.getImageId(), k -> new ArrayList<>());
            comments.add(comment);
            
            // 更新用户统计
            updateUserStats(request.getUserId(), "comment", 1);
            
            // 创建动态
            createFeedItem(request.getUserId(), "comment", request.getImageId(), "评论了图片");
            
            // 发送通知给图片作者
            sendNotification(getImageOwner(request.getImageId()), 
                           getUserName(request.getUserId()) + " 评论了你的图片");
            
            SocialResponse response = SocialResponse.success("评论成功");
            response.setCommentCount(comments.size());
            
            return response;
            
        } catch (Exception e) {
            logger.error("添加评论失败", e);
            return SocialResponse.error("添加评论失败: " + e.getMessage());
        }
    }

    @Override
    public List<SocialResponse.Comment> getComments(String imageId, int page, int size) {
        logger.info("获取评论: imageId={}, page={}, size={}", imageId, page, size);
        
        List<SocialResponse.Comment> comments = imageComments.getOrDefault(imageId, new ArrayList<>());
        
        // 分页处理
        int start = page * size;
        int end = Math.min(start + size, comments.size());
        
        if (start >= comments.size()) {
            return new ArrayList<>();
        }
        
        return comments.subList(start, end);
    }

    @Override
    public SocialResponse deleteComment(String commentId, String userId) {
        try {
            logger.info("删除评论: commentId={}, userId={}", commentId, userId);
            
            // 查找并删除评论
            for (List<SocialResponse.Comment> comments : imageComments.values()) {
                comments.removeIf(comment -> 
                    comment.getCommentId().equals(commentId) && 
                    comment.getUserId().equals(userId));
            }
            
            // 更新用户统计
            updateUserStats(userId, "comment", -1);
            
            return SocialResponse.success("删除评论成功");
            
        } catch (Exception e) {
            logger.error("删除评论失败", e);
            return SocialResponse.error("删除评论失败: " + e.getMessage());
        }
    }

    @Override
    public SocialResponse followUser(String userId, String targetUserId) {
        try {
            logger.info("关注用户: userId={}, targetUserId={}", userId, targetUserId);
            
            if (userId.equals(targetUserId)) {
                return SocialResponse.error("不能关注自己");
            }
            
            Set<String> following = userFollows.computeIfAbsent(userId, k -> new HashSet<>());
            
            if (following.contains(targetUserId)) {
                return SocialResponse.error("已经关注了该用户");
            }
            
            following.add(targetUserId);
            
            // 更新统计
            updateUserStats(userId, "following", 1);
            updateUserStats(targetUserId, "followers", 1);
            
            // 创建动态
            createFeedItem(userId, "follow", null, "关注了用户 " + getUserName(targetUserId));
            
            // 发送通知
            sendNotification(targetUserId, getUserName(userId) + " 关注了你");
            
            SocialResponse response = SocialResponse.success("关注成功");
            response.setFollowing(true);
            
            return response;
            
        } catch (Exception e) {
            logger.error("关注用户失败", e);
            return SocialResponse.error("关注失败: " + e.getMessage());
        }
    }

    @Override
    public SocialResponse unfollowUser(String userId, String targetUserId) {
        try {
            logger.info("取消关注: userId={}, targetUserId={}", userId, targetUserId);
            
            Set<String> following = userFollows.get(userId);
            if (following == null || !following.contains(targetUserId)) {
                return SocialResponse.error("还没有关注该用户");
            }
            
            following.remove(targetUserId);
            
            // 更新统计
            updateUserStats(userId, "following", -1);
            updateUserStats(targetUserId, "followers", -1);
            
            SocialResponse response = SocialResponse.success("取消关注成功");
            response.setFollowing(false);
            
            return response;
            
        } catch (Exception e) {
            logger.error("取消关注失败", e);
            return SocialResponse.error("取消关注失败: " + e.getMessage());
        }
    }

    @Override
    public List<SocialResponse.FeedItem> getUserFeed(String userId, int page, int size) {
        logger.info("获取用户动态: userId={}, page={}, size={}", userId, page, size);
        
        List<SocialResponse.FeedItem> feed = userFeeds.getOrDefault(userId, new ArrayList<>());
        
        // 获取关注用户的动态
        Set<String> following = userFollows.getOrDefault(userId, new HashSet<>());
        for (String followedUserId : following) {
            List<SocialResponse.FeedItem> followedFeed = userFeeds.getOrDefault(followedUserId, new ArrayList<>());
            feed.addAll(followedFeed);
        }
        
        // 按时间排序
        feed.sort((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()));
        
        // 分页处理
        int start = page * size;
        int end = Math.min(start + size, feed.size());
        
        if (start >= feed.size()) {
            return new ArrayList<>();
        }
        
        return feed.subList(start, end);
    }

    @Override
    public List<SocialResponse.TrendingImage> getTrendingImages(int page, int size, String timeRange) {
        logger.info("获取热门图片: page={}, size={}, timeRange={}", page, size, timeRange);

        // 模拟热门图片数据
        List<SocialResponse.TrendingImage> trending = new ArrayList<>();

        for (int i = 1; i <= 50; i++) {
            SocialResponse.TrendingImage image = new SocialResponse.TrendingImage();
            image.setImageId("trending_img_" + i);
            image.setImageUrl("/api/files/trending/" + i + ".jpg");
            image.setThumbnailUrl("/api/files/thumbnails/trending_" + i + ".jpg");
            image.setTitle("热门图片 " + i);
            image.setDescription("这是一张热门图片的描述");
            image.setUserId("user_" + (i % 10 + 1));
            image.setUsername("用户" + (i % 10 + 1));
            image.setAvatar("/api/files/avatars/user_" + (i % 10 + 1) + ".jpg");
            image.setLikeCount(1000 - i * 10);
            image.setCommentCount(100 - i);
            image.setShareCount(50 - i / 2);
            image.setViewCount(5000 - i * 50);
            image.setTrendingScore(100.0 - i);
            image.setRank(i);
            image.setTags(Arrays.asList("热门", "精选", "推荐"));
            image.setCategory("摄影");
            image.setLiked(Math.random() > 0.5);
            image.setCreatedAt(LocalDateTime.now().minusDays(i));
            image.setTrendingAt(LocalDateTime.now().minusHours(i));

            trending.add(image);
        }

        // 分页处理
        int start = page * size;
        int end = Math.min(start + size, trending.size());

        if (start >= trending.size()) {
            return new ArrayList<>();
        }

        return trending.subList(start, end);
    }

    @Override
    public SocialResponse.UserStats getUserStats(String userId) {
        logger.info("获取用户统计: userId={}", userId);

        return userStats.computeIfAbsent(userId, k -> {
            SocialResponse.UserStats stats = new SocialResponse.UserStats();
            stats.setFollowingCount(0);
            stats.setFollowersCount(0);
            stats.setImagesCount(0);
            stats.setTotalLikes(0);
            stats.setTotalComments(0);
            stats.setTotalShares(0);
            stats.setActivityScore(0.0);
            stats.setInfluenceScore(0.0);
            stats.setLastActiveAt(LocalDateTime.now());
            return stats;
        });
    }

    @Override
    public SocialResponse reportContent(SocialRequest request) {
        try {
            logger.info("举报内容: contentId={}, contentType={}, reason={}",
                       request.getContentId(), request.getContentType(), request.getReason());

            // 记录举报信息（实际应存储到数据库）
            String reportId = "report_" + UUID.randomUUID().toString().replace("-", "");

            // 发送通知给管理员
            sendNotification("admin", "收到新的举报: " + request.getReason());

            SocialResponse response = SocialResponse.success("举报成功，我们会尽快处理");
            response.setData(Map.of("reportId", reportId));

            return response;

        } catch (Exception e) {
            logger.error("举报内容失败", e);
            return SocialResponse.error("举报失败: " + e.getMessage());
        }
    }

    @Override
    public SocialResponse generateShareLink(String imageId) {
        try {
            logger.info("生成分享链接: imageId={}", imageId);

            String shareUrl = "https://example.com/share/" + imageId + "?t=" + System.currentTimeMillis();
            shareLinks.put(imageId, shareUrl);

            SocialResponse response = SocialResponse.success("分享链接生成成功");
            response.setShareUrl(shareUrl);

            return response;

        } catch (Exception e) {
            logger.error("生成分享链接失败", e);
            return SocialResponse.error("生成分享链接失败: " + e.getMessage());
        }
    }

    @Override
    public List<SocialResponse> batchOperations(List<SocialRequest> requests) {
        logger.info("批量操作: count={}", requests.size());

        return requests.stream()
                .map(request -> {
                    try {
                        switch (request.getAction()) {
                            case "like":
                                return likeImage(request.getImageId(), request.getUserId());
                            case "unlike":
                                return unlikeImage(request.getImageId(), request.getUserId());
                            case "comment":
                                return addComment(request);
                            case "follow":
                                return followUser(request.getUserId(), request.getTargetUserId());
                            case "unfollow":
                                return unfollowUser(request.getUserId(), request.getTargetUserId());
                            default:
                                return SocialResponse.error("不支持的操作: " + request.getAction());
                        }
                    } catch (Exception e) {
                        return SocialResponse.error("操作失败: " + e.getMessage());
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<SocialResponse.UserStats> getFollowingList(String userId, int page, int size) {
        logger.info("获取关注列表: userId={}, page={}, size={}", userId, page, size);

        Set<String> following = userFollows.getOrDefault(userId, new HashSet<>());
        List<String> followingList = new ArrayList<>(following);

        // 分页处理
        int start = page * size;
        int end = Math.min(start + size, followingList.size());

        if (start >= followingList.size()) {
            return new ArrayList<>();
        }

        return followingList.subList(start, end).stream()
                .map(this::getUserStats)
                .collect(Collectors.toList());
    }

    @Override
    public List<SocialResponse.UserStats> getFollowersList(String userId, int page, int size) {
        logger.info("获取粉丝列表: userId={}, page={}, size={}", userId, page, size);

        // 查找所有关注该用户的用户
        List<String> followers = userFollows.entrySet().stream()
                .filter(entry -> entry.getValue().contains(userId))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 分页处理
        int start = page * size;
        int end = Math.min(start + size, followers.size());

        if (start >= followers.size()) {
            return new ArrayList<>();
        }

        return followers.subList(start, end).stream()
                .map(this::getUserStats)
                .collect(Collectors.toList());
    }

    @Override
    public List<SocialResponse.UserStats> searchUsers(String keyword, int page, int size) {
        logger.info("搜索用户: keyword={}, page={}, size={}", keyword, page, size);

        // 模拟用户搜索
        List<SocialResponse.UserStats> results = new ArrayList<>();

        for (int i = 1; i <= 20; i++) {
            if (("用户" + i).contains(keyword) || ("user_" + i).contains(keyword)) {
                results.add(getUserStats("user_" + i));
            }
        }

        // 分页处理
        int start = page * size;
        int end = Math.min(start + size, results.size());

        if (start >= results.size()) {
            return new ArrayList<>();
        }

        return results.subList(start, end);
    }

    @Override
    public List<SocialResponse.UserStats> getRecommendedUsers(String userId, int count) {
        logger.info("获取推荐用户: userId={}, count={}", userId, count);

        // 模拟用户推荐算法
        List<SocialResponse.UserStats> recommendations = new ArrayList<>();

        for (int i = 1; i <= count; i++) {
            String recommendedUserId = "recommended_user_" + i;
            SocialResponse.UserStats stats = getUserStats(recommendedUserId);
            stats.setInfluenceScore(Math.random() * 100);
            recommendations.add(stats);
        }

        return recommendations;
    }

    @Override
    public SocialResponse getImageInteractionStats(String imageId) {
        logger.info("获取图片互动统计: imageId={}", imageId);

        SocialResponse response = SocialResponse.success("获取统计成功");

        Map<String, Object> stats = new HashMap<>();
        stats.put("likeCount", getLikeCount(imageId));
        stats.put("commentCount", getCommentCount(imageId));
        stats.put("shareCount", getShareCount(imageId));
        stats.put("viewCount", getViewCount(imageId));
        stats.put("engagementRate", calculateEngagementRate(imageId));

        response.setData(stats);

        return response;
    }

    @Override
    public SocialResponse updatePrivacySettings(String userId, SocialRequest.ShareSettings settings) {
        logger.info("更新隐私设置: userId={}", userId);

        // 存储隐私设置（实际应存储到数据库）
        // 这里简化处理

        return SocialResponse.success("隐私设置更新成功");
    }

    @Override
    public List<SocialResponse> getNotifications(String userId, int page, int size) {
        logger.info("获取通知列表: userId={}, page={}, size={}", userId, page, size);

        List<String> userNotifications = notifications.getOrDefault(userId, new ArrayList<>());

        // 分页处理
        int start = page * size;
        int end = Math.min(start + size, userNotifications.size());

        if (start >= userNotifications.size()) {
            return new ArrayList<>();
        }

        return userNotifications.subList(start, end).stream()
                .map(notification -> {
                    SocialResponse response = SocialResponse.success(notification);
                    response.setCreatedAt(LocalDateTime.now().minusHours((int)(Math.random() * 24)));
                    return response;
                })
                .collect(Collectors.toList());
    }

    @Override
    public SocialResponse markNotificationsAsRead(String userId, List<String> notificationIds) {
        logger.info("标记通知为已读: userId={}, count={}", userId, notificationIds.size());

        // 标记通知为已读（实际应更新数据库）

        return SocialResponse.success("通知已标记为已读");
    }

    @Override
    public SocialResponse getSocialActivityStats(String userId, String timeRange) {
        logger.info("获取社交活动统计: userId={}, timeRange={}", userId, timeRange);

        SocialResponse response = SocialResponse.success("获取活动统计成功");

        Map<String, Object> stats = new HashMap<>();
        stats.put("likesGiven", (int)(Math.random() * 100));
        stats.put("likesReceived", (int)(Math.random() * 200));
        stats.put("commentsGiven", (int)(Math.random() * 50));
        stats.put("commentsReceived", (int)(Math.random() * 80));
        stats.put("sharesGiven", (int)(Math.random() * 20));
        stats.put("sharesReceived", (int)(Math.random() * 30));
        stats.put("newFollowers", (int)(Math.random() * 10));
        stats.put("newFollowing", (int)(Math.random() * 15));

        response.setData(stats);

        return response;
    }

    @Override
    public SocialResponse createHashtag(String tag, String description) {
        logger.info("创建话题标签: tag={}, description={}", tag, description);

        trendingHashtags.add(tag);

        return SocialResponse.success("话题标签创建成功");
    }

    @Override
    public List<String> getTrendingHashtags(int count) {
        logger.info("获取热门话题: count={}", count);

        List<String> trending = new ArrayList<>(trendingHashtags);

        // 添加一些默认热门话题
        trending.addAll(Arrays.asList("摄影", "旅行", "美食", "艺术", "生活", "风景", "人像", "街拍"));

        return trending.stream().limit(count).collect(Collectors.toList());
    }

    @Override
    public List<SocialResponse.TrendingImage> getImagesByHashtag(String hashtag, int page, int size) {
        logger.info("根据话题获取图片: hashtag={}, page={}, size={}", hashtag, page, size);

        // 模拟根据话题获取图片
        List<SocialResponse.TrendingImage> images = new ArrayList<>();

        for (int i = 1; i <= 30; i++) {
            SocialResponse.TrendingImage image = new SocialResponse.TrendingImage();
            image.setImageId("hashtag_img_" + hashtag + "_" + i);
            image.setImageUrl("/api/files/hashtag/" + hashtag + "/" + i + ".jpg");
            image.setThumbnailUrl("/api/files/thumbnails/hashtag_" + hashtag + "_" + i + ".jpg");
            image.setTitle(hashtag + " 图片 " + i);
            image.setTags(Arrays.asList(hashtag, "精选"));
            image.setLikeCount((int)(Math.random() * 500));
            image.setCommentCount((int)(Math.random() * 50));
            image.setCreatedAt(LocalDateTime.now().minusDays(i));

            images.add(image);
        }

        // 分页处理
        int start = page * size;
        int end = Math.min(start + size, images.size());

        if (start >= images.size()) {
            return new ArrayList<>();
        }

        return images.subList(start, end);
    }

    @Override
    public SocialResponse blockUser(String userId, String targetUserId) {
        logger.info("屏蔽用户: userId={}, targetUserId={}", userId, targetUserId);

        Set<String> blocked = blockedUsers.computeIfAbsent(userId, k -> new HashSet<>());
        blocked.add(targetUserId);

        // 如果已关注，则取消关注
        Set<String> following = userFollows.get(userId);
        if (following != null) {
            following.remove(targetUserId);
        }

        return SocialResponse.success("用户已屏蔽");
    }

    @Override
    public SocialResponse unblockUser(String userId, String targetUserId) {
        logger.info("取消屏蔽用户: userId={}, targetUserId={}", userId, targetUserId);

        Set<String> blocked = blockedUsers.get(userId);
        if (blocked != null) {
            blocked.remove(targetUserId);
        }

        return SocialResponse.success("已取消屏蔽");
    }

    @Override
    public List<String> getBlockedUsers(String userId) {
        logger.info("获取屏蔽列表: userId={}", userId);

        Set<String> blocked = blockedUsers.getOrDefault(userId, new HashSet<>());
        return new ArrayList<>(blocked);
    }

    @Override
    public boolean hasPermission(String userId, String action, String targetId) {
        logger.info("验证用户权限: userId={}, action={}, targetId={}", userId, action, targetId);

        // 检查是否被屏蔽
        Set<String> blocked = blockedUsers.getOrDefault(targetId, new HashSet<>());
        if (blocked.contains(userId)) {
            return false;
        }

        // 根据操作类型检查权限
        switch (action) {
            case "view":
                return true; // 默认所有人都可以查看
            case "comment":
                return !blocked.contains(userId);
            case "like":
                return !blocked.contains(userId);
            case "share":
                return true;
            default:
                return false;
        }
    }

    @Override
    public int cleanupExpiredData(int daysOld) {
        logger.info("清理过期数据: daysOld={}", daysOld);

        int cleanedCount = 0;
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);

        // 清理过期的动态
        for (List<SocialResponse.FeedItem> feed : userFeeds.values()) {
            int originalSize = feed.size();
            feed.removeIf(item -> item.getCreatedAt().isBefore(cutoffDate));
            cleanedCount += originalSize - feed.size();
        }

        // 清理过期的通知
        for (List<String> userNotifications : notifications.values()) {
            int originalSize = userNotifications.size();
            // 简化处理，清理一半通知
            if (userNotifications.size() > 100) {
                userNotifications.subList(100, userNotifications.size()).clear();
                cleanedCount += originalSize - userNotifications.size();
            }
        }

        logger.info("清理完成，共清理 {} 条过期数据", cleanedCount);
        return cleanedCount;
    }

    @Override
    public String exportUserSocialData(String userId) {
        logger.info("导出用户社交数据: userId={}", userId);

        Map<String, Object> exportData = new HashMap<>();
        exportData.put("userStats", getUserStats(userId));
        exportData.put("following", userFollows.getOrDefault(userId, new HashSet<>()));
        exportData.put("followers", getFollowersList(userId, 0, Integer.MAX_VALUE));
        exportData.put("feed", userFeeds.getOrDefault(userId, new ArrayList<>()));
        exportData.put("notifications", notifications.getOrDefault(userId, new ArrayList<>()));
        exportData.put("blockedUsers", blockedUsers.getOrDefault(userId, new HashSet<>()));

        // 简化的JSON导出（实际应使用JSON序列化）
        return exportData.toString();
    }

    @Override
    public double calculateInfluenceScore(String userId) {
        logger.info("计算用户影响力分数: userId={}", userId);

        SocialResponse.UserStats stats = getUserStats(userId);

        // 简化的影响力计算公式
        double score = stats.getFollowersCount() * 0.3 +
                      stats.getTotalLikes() * 0.2 +
                      stats.getTotalComments() * 0.25 +
                      stats.getTotalShares() * 0.25;

        // 更新用户统计
        stats.setInfluenceScore(score);

        return score;
    }

    @Override
    public List<SocialResponse.UserStats> getSimilarUsers(String userId, int count) {
        logger.info("获取相似用户推荐: userId={}, count={}", userId, count);

        // 模拟相似用户推荐算法
        List<SocialResponse.UserStats> similarUsers = new ArrayList<>();

        SocialResponse.UserStats currentUser = getUserStats(userId);

        for (int i = 1; i <= count; i++) {
            String similarUserId = "similar_user_" + i;
            SocialResponse.UserStats stats = getUserStats(similarUserId);

            // 模拟相似度计算
            double similarity = Math.random() * 0.5 + 0.5; // 0.5-1.0之间的相似度
            stats.setActivityScore(similarity * 100);

            similarUsers.add(stats);
        }

        return similarUsers;
    }

    // 私有辅助方法
    private String generateShareUrl(String imageId, String platform) {
        String baseUrl = "https://example.com/share/" + imageId;
        switch (platform.toLowerCase()) {
            case "weibo":
                return "https://service.weibo.com/share/share.php?url=" + baseUrl;
            case "wechat":
                return baseUrl + "?platform=wechat";
            case "qq":
                return "https://connect.qq.com/widget/shareqq/index.html?url=" + baseUrl;
            default:
                return baseUrl + "?platform=" + platform;
        }
    }

    private String getPlatformName(String platform) {
        Map<String, String> platformNames = new HashMap<>();
        platformNames.put("weibo", "微博");
        platformNames.put("wechat", "微信");
        platformNames.put("qq", "QQ");
        platformNames.put("douyin", "抖音");
        platformNames.put("xiaohongshu", "小红书");

        return platformNames.getOrDefault(platform, platform);
    }

    private void updateShareStats(String imageId, String userId) {
        // 更新分享统计
        updateUserStats(userId, "share", 1);
    }

    private void updateUserStats(String userId, String type, int delta) {
        SocialResponse.UserStats stats = getUserStats(userId);

        switch (type) {
            case "like":
                stats.setTotalLikes(Math.max(0, stats.getTotalLikes() + delta));
                break;
            case "comment":
                stats.setTotalComments(Math.max(0, stats.getTotalComments() + delta));
                break;
            case "share":
                stats.setTotalShares(Math.max(0, stats.getTotalShares() + delta));
                break;
            case "following":
                stats.setFollowingCount(Math.max(0, stats.getFollowingCount() + delta));
                break;
            case "followers":
                stats.setFollowersCount(Math.max(0, stats.getFollowersCount() + delta));
                break;
        }

        stats.setLastActiveAt(LocalDateTime.now());

        // 重新计算活跃度分数
        double activityScore = (stats.getTotalLikes() + stats.getTotalComments() * 2 + stats.getTotalShares() * 3) / 10.0;
        stats.setActivityScore(activityScore);
    }

    private void createFeedItem(String userId, String type, String imageId, String content) {
        SocialResponse.FeedItem feedItem = new SocialResponse.FeedItem();
        feedItem.setFeedId("feed_" + UUID.randomUUID().toString().replace("-", ""));
        feedItem.setUserId(userId);
        feedItem.setUsername(getUserName(userId));
        feedItem.setAvatar(getUserAvatar(userId));
        feedItem.setType(type);
        feedItem.setImageId(imageId);
        feedItem.setContent(content);
        feedItem.setLikeCount(0);
        feedItem.setCommentCount(0);
        feedItem.setShareCount(0);
        feedItem.setLiked(false);
        feedItem.setCreatedAt(LocalDateTime.now());

        List<SocialResponse.FeedItem> feed = userFeeds.computeIfAbsent(userId, k -> new ArrayList<>());
        feed.add(0, feedItem); // 添加到列表开头

        // 限制动态数量
        if (feed.size() > 1000) {
            feed.subList(1000, feed.size()).clear();
        }
    }

    private void sendNotification(String userId, String message) {
        List<String> userNotifications = notifications.computeIfAbsent(userId, k -> new ArrayList<>());
        userNotifications.add(0, message); // 添加到列表开头

        // 限制通知数量
        if (userNotifications.size() > 200) {
            userNotifications.subList(200, userNotifications.size()).clear();
        }
    }

    private String getUserName(String userId) {
        return "用户" + userId.replaceAll("\\D", "");
    }

    private String getUserAvatar(String userId) {
        return "/api/files/avatars/" + userId + ".jpg";
    }

    private String getImageOwner(String imageId) {
        // 模拟获取图片所有者
        return "user_" + (Math.abs(imageId.hashCode()) % 10 + 1);
    }

    private int getLikeCount(String imageId) {
        Set<String> likes = imageLikes.get(imageId);
        return likes != null ? likes.size() : 0;
    }

    private int getCommentCount(String imageId) {
        List<SocialResponse.Comment> comments = imageComments.get(imageId);
        return comments != null ? comments.size() : 0;
    }

    private int getShareCount(String imageId) {
        // 模拟分享数统计
        return (int)(Math.random() * 50);
    }

    private int getViewCount(String imageId) {
        // 模拟浏览数统计
        return (int)(Math.random() * 1000) + 100;
    }

    private double calculateEngagementRate(String imageId) {
        int likes = getLikeCount(imageId);
        int comments = getCommentCount(imageId);
        int shares = getShareCount(imageId);
        int views = getViewCount(imageId);

        if (views == 0) return 0.0;

        return (double)(likes + comments * 2 + shares * 3) / views * 100;
    }
}
