package com.example.photoupload.service.impl;

import com.example.photoupload.dto.ThreeDViewRequest;
import com.example.photoupload.dto.ThreeDViewResponse;
import com.example.photoupload.service.ThreeDImageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.UUID;

/**
 * 3D图像服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class ThreeDImageServiceImpl implements ThreeDImageService {

    private static final Logger logger = LoggerFactory.getLogger(ThreeDImageServiceImpl.class);

    @Value("${file.upload-dir}")
    private String uploadDir;

    @Value("${file.3d-models-dir:3d-models}")
    private String modelsDir;

    @Override
    public ThreeDViewResponse generate3DModel(ThreeDViewRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("开始生成3D模型: fileName={}, modelType={}", 
                       request.getFileName(), request.getModelType());

            // 生成唯一模型ID
            String modelId = "model_" + UUID.randomUUID().toString().replace("-", "");
            
            // 创建模型目录
            Path modelPath = createModelDirectory(modelId);
            
            // 加载原始图像
            BufferedImage originalImage = loadImage(request.getFileName());
            if (originalImage == null) {
                return ThreeDViewResponse.error("无法加载图像文件");
            }

            // 生成深度图
            BufferedImage depthMap = generateDepthMapImage(originalImage, request.getDepthIntensity());
            
            // 生成3D模型文件
            generateOBJModel(originalImage, depthMap, modelPath, request);
            
            // 生成纹理文件
            generateTextureFile(originalImage, modelPath, modelId);
            
            // 生成预览图
            generatePreviewImage(originalImage, modelPath, modelId);
            
            // 创建响应
            ThreeDViewResponse response = createSuccessResponse(modelId, request);
            response.setProcessingTime(System.currentTimeMillis() - startTime);
            
            logger.info("3D模型生成完成: modelId={}, 耗时={}ms", 
                       modelId, response.getProcessingTime());
            
            return response;
            
        } catch (Exception e) {
            logger.error("生成3D模型失败", e);
            return ThreeDViewResponse.error("生成3D模型失败: " + e.getMessage());
        }
    }

    @Override
    public Resource get3DModelFile(String modelId) throws IOException {
        Path modelFile = Paths.get(uploadDir, modelsDir, modelId, modelId + ".obj");
        if (!Files.exists(modelFile)) {
            throw new IOException("模型文件不存在: " + modelId);
        }
        return new FileSystemResource(modelFile);
    }

    @Override
    public Resource get3DTextureFile(String modelId) throws IOException {
        Path textureFile = Paths.get(uploadDir, modelsDir, modelId, modelId + "_texture.jpg");
        if (!Files.exists(textureFile)) {
            throw new IOException("纹理文件不存在: " + modelId);
        }
        return new FileSystemResource(textureFile);
    }

    @Override
    public ThreeDViewResponse generatePanorama(ThreeDViewRequest request) {
        try {
            logger.info("生成全景图: fileName={}", request.getFileName());
            
            BufferedImage originalImage = loadImage(request.getFileName());
            if (originalImage == null) {
                return ThreeDViewResponse.error("无法加载图像文件");
            }

            // 生成球形全景图
            BufferedImage panorama = createSphericalPanorama(originalImage);
            
            String modelId = "panorama_" + UUID.randomUUID().toString().replace("-", "");
            Path modelPath = createModelDirectory(modelId);
            
            // 保存全景图
            File panoramaFile = modelPath.resolve(modelId + "_panorama.jpg").toFile();
            ImageIO.write(panorama, "jpg", panoramaFile);
            
            ThreeDViewResponse response = ThreeDViewResponse.success(modelId, "全景图生成成功");
            response.setModelType("panorama");
            response.setTextureUrl("/api/3d/texture/" + modelId);
            
            return response;
            
        } catch (Exception e) {
            logger.error("生成全景图失败", e);
            return ThreeDViewResponse.error("生成全景图失败: " + e.getMessage());
        }
    }

    @Override
    public ThreeDViewResponse generateStereoscopic(ThreeDViewRequest request) {
        try {
            logger.info("生成立体图像: fileName={}", request.getFileName());
            
            BufferedImage originalImage = loadImage(request.getFileName());
            if (originalImage == null) {
                return ThreeDViewResponse.error("无法加载图像文件");
            }

            // 生成左右眼图像
            BufferedImage leftEye = generateLeftEyeView(originalImage, request.getDepthIntensity());
            BufferedImage rightEye = generateRightEyeView(originalImage, request.getDepthIntensity());
            
            String modelId = "stereo_" + UUID.randomUUID().toString().replace("-", "");
            Path modelPath = createModelDirectory(modelId);
            
            // 保存立体图像
            ImageIO.write(leftEye, "jpg", modelPath.resolve(modelId + "_left.jpg").toFile());
            ImageIO.write(rightEye, "jpg", modelPath.resolve(modelId + "_right.jpg").toFile());
            
            ThreeDViewResponse response = ThreeDViewResponse.success(modelId, "立体图像生成成功");
            response.setModelType("stereoscopic");
            
            return response;
            
        } catch (Exception e) {
            logger.error("生成立体图像失败", e);
            return ThreeDViewResponse.error("生成立体图像失败: " + e.getMessage());
        }
    }

    @Override
    public ThreeDViewResponse get3DViewerConfig() {
        ThreeDViewResponse response = new ThreeDViewResponse();
        response.setSuccess(true);
        response.setMessage("获取配置成功");
        
        // 创建默认查看器配置
        ThreeDViewResponse.ViewerConfig config = new ThreeDViewResponse.ViewerConfig();
        config.setEnableControls(true);
        config.setAutoRotate(true);
        config.setRotationSpeed(1.0f);
        
        response.setViewerConfig(config);
        
        return response;
    }

    @Override
    public boolean delete3DModel(String modelId) {
        try {
            Path modelPath = Paths.get(uploadDir, modelsDir, modelId);
            if (Files.exists(modelPath)) {
                deleteDirectory(modelPath);
                logger.info("删除3D模型成功: modelId={}", modelId);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("删除3D模型失败: modelId={}", modelId, e);
            return false;
        }
    }

    @Override
    public byte[] generateDepthMap(String fileName) {
        try {
            BufferedImage originalImage = loadImage(fileName);
            if (originalImage == null) {
                return null;
            }
            
            BufferedImage depthMap = generateDepthMapImage(originalImage, 0.5f);
            
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(depthMap, "jpg", baos);
            return baos.toByteArray();
            
        } catch (Exception e) {
            logger.error("生成深度图失败", e);
            return null;
        }
    }

    @Override
    public byte[] createCubeMap(String fileName, ThreeDViewRequest request) {
        // 立方体贴图生成逻辑
        return new byte[0];
    }

    @Override
    public byte[] generateNormalMap(String fileName, float intensity) {
        // 法线贴图生成逻辑
        return new byte[0];
    }

    @Override
    public byte[] createSphericalProjection(String fileName) {
        // 球形投影生成逻辑
        return new byte[0];
    }

    @Override
    public byte[] generateDisplacementMap(String fileName, float heightScale) {
        // 位移贴图生成逻辑
        return new byte[0];
    }

    @Override
    public byte[] createEnvironmentMap(String fileName) {
        // 环境贴图生成逻辑
        return new byte[0];
    }

    @Override
    public boolean validateModel(String modelId) {
        Path modelFile = Paths.get(uploadDir, modelsDir, modelId, modelId + ".obj");
        return Files.exists(modelFile);
    }

    @Override
    public ThreeDViewResponse.ModelInfo getModelStatistics(String modelId) {
        // 获取模型统计信息
        return new ThreeDViewResponse.ModelInfo(1024, 2048, "1024x1024", 1048576, "OBJ");
    }

    @Override
    public String optimizeModel(String modelId, String quality) {
        // 模型优化逻辑
        return modelId + "_optimized";
    }

    @Override
    public byte[] generateModelThumbnail(String modelId, int width, int height) {
        // 生成模型缩略图
        return new byte[0];
    }

    // 私有辅助方法
    private Path createModelDirectory(String modelId) throws IOException {
        Path modelPath = Paths.get(uploadDir, modelsDir, modelId);
        Files.createDirectories(modelPath);
        return modelPath;
    }

    private BufferedImage loadImage(String fileName) {
        try {
            Path imagePath = Paths.get(uploadDir, fileName);
            if (Files.exists(imagePath)) {
                return ImageIO.read(imagePath.toFile());
            }
        } catch (Exception e) {
            logger.error("加载图像失败: fileName={}", fileName, e);
        }
        return null;
    }

    private BufferedImage generateDepthMapImage(BufferedImage image, float intensity) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage depthMap = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        
        Graphics2D g2d = depthMap.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 简单的深度图生成：基于亮度
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int gray = (int) (((rgb >> 16) & 0xFF) * 0.299 + 
                                 ((rgb >> 8) & 0xFF) * 0.587 + 
                                 (rgb & 0xFF) * 0.114);
                
                // 应用深度强度
                gray = (int) (gray * intensity);
                gray = Math.max(0, Math.min(255, gray));
                
                int depthColor = (gray << 16) | (gray << 8) | gray;
                depthMap.setRGB(x, y, depthColor);
            }
        }
        
        g2d.dispose();
        return depthMap;
    }

    private void generateOBJModel(BufferedImage image, BufferedImage depthMap, Path modelPath, ThreeDViewRequest request) throws IOException {
        String modelId = modelPath.getFileName().toString();
        File objFile = modelPath.resolve(modelId + ".obj").toFile();
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(objFile))) {
            writer.println("# OBJ file generated by PhotoMagic");
            writer.println("# Model: " + modelId);
            writer.println();
            
            int width = image.getWidth();
            int height = image.getHeight();
            
            // 生成顶点
            writer.println("# Vertices");
            for (int y = 0; y < height; y += 4) { // 降低分辨率以减少顶点数
                for (int x = 0; x < width; x += 4) {
                    float normalizedX = (float) x / width - 0.5f;
                    float normalizedY = (float) y / height - 0.5f;
                    
                    // 从深度图获取Z坐标
                    int depthValue = depthMap.getRGB(x, y) & 0xFF;
                    float normalizedZ = (depthValue / 255.0f) * request.getDepthIntensity();
                    
                    writer.printf("v %.6f %.6f %.6f%n", normalizedX, normalizedY, normalizedZ);
                }
            }
            
            // 生成纹理坐标
            writer.println("# Texture coordinates");
            for (int y = 0; y < height; y += 4) {
                for (int x = 0; x < width; x += 4) {
                    float u = (float) x / width;
                    float v = 1.0f - (float) y / height; // 翻转V坐标
                    writer.printf("vt %.6f %.6f%n", u, v);
                }
            }
            
            // 生成面
            writer.println("# Faces");
            int verticesPerRow = width / 4;
            for (int y = 0; y < height / 4 - 1; y++) {
                for (int x = 0; x < width / 4 - 1; x++) {
                    int v1 = y * verticesPerRow + x + 1;
                    int v2 = y * verticesPerRow + x + 2;
                    int v3 = (y + 1) * verticesPerRow + x + 1;
                    int v4 = (y + 1) * verticesPerRow + x + 2;
                    
                    // 第一个三角形
                    writer.printf("f %d/%d %d/%d %d/%d%n", v1, v1, v2, v2, v3, v3);
                    // 第二个三角形
                    writer.printf("f %d/%d %d/%d %d/%d%n", v2, v2, v4, v4, v3, v3);
                }
            }
        }
    }

    private void generateTextureFile(BufferedImage image, Path modelPath, String modelId) throws IOException {
        File textureFile = modelPath.resolve(modelId + "_texture.jpg").toFile();
        ImageIO.write(image, "jpg", textureFile);
    }

    private void generatePreviewImage(BufferedImage image, Path modelPath, String modelId) throws IOException {
        // 生成预览图（缩略图）
        int previewWidth = 300;
        int previewHeight = 200;
        
        BufferedImage preview = new BufferedImage(previewWidth, previewHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = preview.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(image, 0, 0, previewWidth, previewHeight, null);
        g2d.dispose();
        
        File previewFile = modelPath.resolve(modelId + "_preview.jpg").toFile();
        ImageIO.write(preview, "jpg", previewFile);
    }

    private ThreeDViewResponse createSuccessResponse(String modelId, ThreeDViewRequest request) {
        ThreeDViewResponse response = ThreeDViewResponse.success(modelId, "3D模型生成成功");
        response.setModelType(request.getModelType());
        response.setModelUrl("/api/3d/model/" + modelId);
        response.setTextureUrl("/api/3d/texture/" + modelId);
        response.setPreviewUrl("/api/files/preview/" + modelId + "_preview.jpg");
        
        // 设置模型信息
        ThreeDViewResponse.ModelInfo modelInfo = new ThreeDViewResponse.ModelInfo();
        modelInfo.setFormat("OBJ");
        modelInfo.setVertexCount(1024);
        modelInfo.setFaceCount(2048);
        modelInfo.setTextureResolution("1024x1024");
        response.setModelInfo(modelInfo);
        
        // 设置查看器配置
        response.setViewerConfig(new ThreeDViewResponse.ViewerConfig());
        
        return response;
    }

    private BufferedImage createSphericalPanorama(BufferedImage image) {
        // 简化的球形全景图生成
        return image; // 实际实现需要复杂的投影算法
    }

    private BufferedImage generateLeftEyeView(BufferedImage image, float depthIntensity) {
        // 生成左眼视图（向左偏移）
        return image; // 简化实现
    }

    private BufferedImage generateRightEyeView(BufferedImage image, float depthIntensity) {
        // 生成右眼视图（向右偏移）
        return image; // 简化实现
    }

    private void deleteDirectory(Path path) throws IOException {
        Files.walk(path)
             .sorted((a, b) -> b.compareTo(a)) // 反向排序，先删除文件再删除目录
             .forEach(p -> {
                 try {
                     Files.delete(p);
                 } catch (IOException e) {
                     logger.error("删除文件失败: {}", p, e);
                 }
             });
    }
}
