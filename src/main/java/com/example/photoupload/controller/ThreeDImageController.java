package com.example.photoupload.controller;

import com.example.photoupload.dto.ThreeDViewRequest;
import com.example.photoupload.dto.ThreeDViewResponse;
import com.example.photoupload.service.ThreeDImageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * 3D图像查看器控制器
 * 提供3D图像展示和交互功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/3d")
@Tag(name = "3D图像查看器", description = "3D图像展示和交互API")
public class ThreeDImageController {

    private static final Logger logger = LoggerFactory.getLogger(ThreeDImageController.class);

    @Autowired
    private ThreeDImageService threeDImageService;

    /**
     * 生成3D图像模型
     */
    @PostMapping("/generate")
    @Operation(summary = "生成3D图像模型", description = "将2D图像转换为3D模型")
    public ResponseEntity<ThreeDViewResponse> generate3DModel(@RequestBody ThreeDViewRequest request) {
        try {
            logger.info("生成3D模型请求: fileName={}", request.getFileName());
            
            ThreeDViewResponse response = threeDImageService.generate3DModel(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("生成3D模型失败", e);
            ThreeDViewResponse errorResponse = new ThreeDViewResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("生成3D模型失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取3D模型文件
     */
    @GetMapping("/model/{modelId}")
    @Operation(summary = "获取3D模型文件", description = "下载生成的3D模型文件")
    public ResponseEntity<Resource> get3DModel(@PathVariable String modelId) {
        try {
            logger.info("获取3D模型: modelId={}", modelId);
            
            Resource resource = threeDImageService.get3DModelFile(modelId);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + modelId + ".obj\"")
                    .body(resource);
                    
        } catch (IOException e) {
            logger.error("获取3D模型文件失败: modelId={}", modelId, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取3D纹理文件
     */
    @GetMapping("/texture/{modelId}")
    @Operation(summary = "获取3D纹理文件", description = "获取3D模型的纹理贴图")
    public ResponseEntity<Resource> get3DTexture(@PathVariable String modelId) {
        try {
            logger.info("获取3D纹理: modelId={}", modelId);
            
            Resource resource = threeDImageService.get3DTextureFile(modelId);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_JPEG)
                    .body(resource);
                    
        } catch (IOException e) {
            logger.error("获取3D纹理文件失败: modelId={}", modelId, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 生成全景图
     */
    @PostMapping("/panorama")
    @Operation(summary = "生成全景图", description = "将图像转换为360度全景图")
    public ResponseEntity<ThreeDViewResponse> generatePanorama(@RequestBody ThreeDViewRequest request) {
        try {
            logger.info("生成全景图请求: fileName={}", request.getFileName());
            
            ThreeDViewResponse response = threeDImageService.generatePanorama(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("生成全景图失败", e);
            ThreeDViewResponse errorResponse = new ThreeDViewResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("生成全景图失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 生成立体图像
     */
    @PostMapping("/stereoscopic")
    @Operation(summary = "生成立体图像", description = "生成左右眼立体图像对")
    public ResponseEntity<ThreeDViewResponse> generateStereoscopic(@RequestBody ThreeDViewRequest request) {
        try {
            logger.info("生成立体图像请求: fileName={}", request.getFileName());
            
            ThreeDViewResponse response = threeDImageService.generateStereoscopic(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("生成立体图像失败", e);
            ThreeDViewResponse errorResponse = new ThreeDViewResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("生成立体图像失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取3D查看器配置
     */
    @GetMapping("/config")
    @Operation(summary = "获取3D查看器配置", description = "获取3D查看器的配置信息")
    public ResponseEntity<ThreeDViewResponse> get3DViewerConfig() {
        try {
            ThreeDViewResponse response = threeDImageService.get3DViewerConfig();
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取3D查看器配置失败", e);
            ThreeDViewResponse errorResponse = new ThreeDViewResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("获取配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 删除3D模型
     */
    @DeleteMapping("/model/{modelId}")
    @Operation(summary = "删除3D模型", description = "删除指定的3D模型文件")
    public ResponseEntity<ThreeDViewResponse> delete3DModel(@PathVariable String modelId) {
        try {
            logger.info("删除3D模型: modelId={}", modelId);
            
            boolean deleted = threeDImageService.delete3DModel(modelId);
            
            ThreeDViewResponse response = new ThreeDViewResponse();
            response.setSuccess(deleted);
            response.setMessage(deleted ? "删除成功" : "删除失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除3D模型失败: modelId={}", modelId, e);
            ThreeDViewResponse errorResponse = new ThreeDViewResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
}
