package com.example.photoupload.controller;

import com.example.photoupload.dto.SocialRequest;
import com.example.photoupload.dto.SocialResponse;
import com.example.photoupload.service.SocialService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社交分享控制器
 * 提供社交媒体分享、评论、点赞等功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/social")
@Tag(name = "社交分享", description = "社交媒体分享和互动API")
public class SocialController {

    private static final Logger logger = LoggerFactory.getLogger(SocialController.class);

    @Autowired
    private SocialService socialService;

    /**
     * 分享图片到社交媒体
     */
    @PostMapping("/share")
    @Operation(summary = "分享图片", description = "将图片分享到指定的社交媒体平台")
    public ResponseEntity<SocialResponse> shareImage(@RequestBody SocialRequest request) {
        try {
            logger.info("分享图片: imageId={}, platform={}", request.getImageId(), request.getPlatform());
            
            SocialResponse response = socialService.shareImage(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("分享图片失败", e);
            SocialResponse errorResponse = SocialResponse.error("分享失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 点赞图片
     */
    @PostMapping("/like/{imageId}")
    @Operation(summary = "点赞图片", description = "为指定图片点赞")
    public ResponseEntity<SocialResponse> likeImage(
            @PathVariable String imageId,
            @RequestParam String userId) {
        try {
            logger.info("点赞图片: imageId={}, userId={}", imageId, userId);
            
            SocialResponse response = socialService.likeImage(imageId, userId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("点赞失败", e);
            SocialResponse errorResponse = SocialResponse.error("点赞失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 取消点赞
     */
    @DeleteMapping("/like/{imageId}")
    @Operation(summary = "取消点赞", description = "取消对指定图片的点赞")
    public ResponseEntity<SocialResponse> unlikeImage(
            @PathVariable String imageId,
            @RequestParam String userId) {
        try {
            logger.info("取消点赞: imageId={}, userId={}", imageId, userId);
            
            SocialResponse response = socialService.unlikeImage(imageId, userId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("取消点赞失败", e);
            SocialResponse errorResponse = SocialResponse.error("取消点赞失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 添加评论
     */
    @PostMapping("/comment")
    @Operation(summary = "添加评论", description = "为图片添加评论")
    public ResponseEntity<SocialResponse> addComment(@RequestBody SocialRequest request) {
        try {
            logger.info("添加评论: imageId={}, userId={}", request.getImageId(), request.getUserId());
            
            SocialResponse response = socialService.addComment(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("添加评论失败", e);
            SocialResponse errorResponse = SocialResponse.error("添加评论失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取图片评论
     */
    @GetMapping("/comments/{imageId}")
    @Operation(summary = "获取评论", description = "获取指定图片的所有评论")
    public ResponseEntity<List<SocialResponse.Comment>> getComments(
            @PathVariable String imageId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            logger.info("获取评论: imageId={}, page={}, size={}", imageId, page, size);
            
            List<SocialResponse.Comment> comments = socialService.getComments(imageId, page, size);
            
            return ResponseEntity.ok(comments);
            
        } catch (Exception e) {
            logger.error("获取评论失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除评论
     */
    @DeleteMapping("/comment/{commentId}")
    @Operation(summary = "删除评论", description = "删除指定的评论")
    public ResponseEntity<SocialResponse> deleteComment(
            @PathVariable String commentId,
            @RequestParam String userId) {
        try {
            logger.info("删除评论: commentId={}, userId={}", commentId, userId);
            
            SocialResponse response = socialService.deleteComment(commentId, userId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除评论失败", e);
            SocialResponse errorResponse = SocialResponse.error("删除评论失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 关注用户
     */
    @PostMapping("/follow/{targetUserId}")
    @Operation(summary = "关注用户", description = "关注指定用户")
    public ResponseEntity<SocialResponse> followUser(
            @PathVariable String targetUserId,
            @RequestParam String userId) {
        try {
            logger.info("关注用户: userId={}, targetUserId={}", userId, targetUserId);
            
            SocialResponse response = socialService.followUser(userId, targetUserId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("关注用户失败", e);
            SocialResponse errorResponse = SocialResponse.error("关注失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 取消关注
     */
    @DeleteMapping("/follow/{targetUserId}")
    @Operation(summary = "取消关注", description = "取消关注指定用户")
    public ResponseEntity<SocialResponse> unfollowUser(
            @PathVariable String targetUserId,
            @RequestParam String userId) {
        try {
            logger.info("取消关注: userId={}, targetUserId={}", userId, targetUserId);
            
            SocialResponse response = socialService.unfollowUser(userId, targetUserId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("取消关注失败", e);
            SocialResponse errorResponse = SocialResponse.error("取消关注失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取用户动态
     */
    @GetMapping("/feed/{userId}")
    @Operation(summary = "获取用户动态", description = "获取用户关注的人的最新动态")
    public ResponseEntity<List<SocialResponse.FeedItem>> getUserFeed(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            logger.info("获取用户动态: userId={}, page={}, size={}", userId, page, size);
            
            List<SocialResponse.FeedItem> feed = socialService.getUserFeed(userId, page, size);
            
            return ResponseEntity.ok(feed);
            
        } catch (Exception e) {
            logger.error("获取用户动态失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取热门图片
     */
    @GetMapping("/trending")
    @Operation(summary = "获取热门图片", description = "获取当前热门的图片")
    public ResponseEntity<List<SocialResponse.TrendingImage>> getTrendingImages(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "day") String timeRange) {
        try {
            logger.info("获取热门图片: page={}, size={}, timeRange={}", page, size, timeRange);
            
            List<SocialResponse.TrendingImage> trending = socialService.getTrendingImages(page, size, timeRange);
            
            return ResponseEntity.ok(trending);
            
        } catch (Exception e) {
            logger.error("获取热门图片失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats/{userId}")
    @Operation(summary = "获取用户统计", description = "获取用户的社交统计信息")
    public ResponseEntity<SocialResponse.UserStats> getUserStats(@PathVariable String userId) {
        try {
            logger.info("获取用户统计: userId={}", userId);
            
            SocialResponse.UserStats stats = socialService.getUserStats(userId);
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            logger.error("获取用户统计失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 举报内容
     */
    @PostMapping("/report")
    @Operation(summary = "举报内容", description = "举报不当内容")
    public ResponseEntity<SocialResponse> reportContent(@RequestBody SocialRequest request) {
        try {
            logger.info("举报内容: contentId={}, contentType={}, reason={}", 
                       request.getContentId(), request.getContentType(), request.getReason());
            
            SocialResponse response = socialService.reportContent(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("举报内容失败", e);
            SocialResponse errorResponse = SocialResponse.error("举报失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取分享链接
     */
    @GetMapping("/share-link/{imageId}")
    @Operation(summary = "获取分享链接", description = "生成图片的分享链接")
    public ResponseEntity<SocialResponse> getShareLink(@PathVariable String imageId) {
        try {
            logger.info("获取分享链接: imageId={}", imageId);
            
            SocialResponse response = socialService.generateShareLink(imageId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取分享链接失败", e);
            SocialResponse errorResponse = SocialResponse.error("获取分享链接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 批量操作
     */
    @PostMapping("/batch")
    @Operation(summary = "批量操作", description = "批量执行社交操作")
    public ResponseEntity<List<SocialResponse>> batchOperations(@RequestBody List<SocialRequest> requests) {
        try {
            logger.info("批量操作: count={}", requests.size());
            
            List<SocialResponse> responses = socialService.batchOperations(requests);
            
            return ResponseEntity.ok(responses);
            
        } catch (Exception e) {
            logger.error("批量操作失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
}
