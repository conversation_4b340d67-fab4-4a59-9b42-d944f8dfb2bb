<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 3D图像查看器 - PhotoMagic</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/controls/OrbitControls.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/loaders/OBJLoader.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow: hidden;
        }

        .viewer-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
        }

        .viewer-canvas {
            flex: 1;
            position: relative;
        }

        #threejs-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .control-panel {
            width: 350px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 2rem;
            overflow-y: auto;
            border-left: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-section h3 {
            margin-bottom: 1rem;
            color: #64b5f6;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-group {
            margin-bottom: 1.5rem;
        }

        .control-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #e3f2fd;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #64b5f6;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .slider-value {
            min-width: 50px;
            text-align: center;
            font-weight: bold;
            color: #64b5f6;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0.25rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #64b5f6, #42a5f5);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #78909c, #607d8b);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #81c784, #66bb6a);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #e57373, #ef5350);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .mode-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
        }

        .mode-btn {
            padding: 0.5rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            color: white;
        }

        .mode-btn:hover, .mode-btn.active {
            border-color: #64b5f6;
            background: rgba(100, 181, 246, 0.2);
            color: #64b5f6;
        }

        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #64b5f6;
            background: rgba(100, 181, 246, 0.1);
        }

        .upload-area i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #64b5f6;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 6px solid rgba(255, 255, 255, 0.1);
            border-top: 6px solid #64b5f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 1rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .info-label {
            color: #b0bec5;
        }

        .info-value {
            color: #64b5f6;
            font-weight: bold;
        }

        .fullscreen-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            color: white;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .fullscreen-btn:hover {
            background: rgba(100, 181, 246, 0.7);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #64b5f6;
            z-index: 1001;
            animation: slideInRight 0.3s ease;
        }

        .notification.error {
            border-left-color: #f44336;
        }

        .notification.success {
            border-left-color: #4caf50;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .viewer-container {
                flex-direction: column;
            }

            .control-panel {
                width: 100%;
                height: 40vh;
                order: 2;
            }

            .viewer-canvas {
                height: 60vh;
                order: 1;
            }
        }
    </style>
</head>
<body>
    <div class="viewer-container">
        <!-- 3D查看器画布 -->
        <div class="viewer-canvas">
            <canvas id="threejs-canvas"></canvas>

            <!-- 信息面板 -->
            <div class="info-panel" id="infoPanel">
                <div class="info-item">
                    <span class="info-label">顶点数:</span>
                    <span class="info-value" id="vertexCount">0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">面数:</span>
                    <span class="info-value" id="faceCount">0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">FPS:</span>
                    <span class="info-value" id="fpsCounter">60</span>
                </div>
                <div class="info-item">
                    <span class="info-label">模式:</span>
                    <span class="info-value" id="currentMode">3D模型</span>
                </div>
            </div>

            <!-- 全屏按钮 -->
            <button class="fullscreen-btn" id="fullscreenBtn" title="全屏">
                <i class="fas fa-expand"></i>
            </button>

            <!-- 加载遮罩 -->
            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                <div class="spinner"></div>
                <h3>正在生成3D模型...</h3>
                <p>请稍候，这可能需要几秒钟</p>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <!-- 文件上传 -->
            <div class="panel-section">
                <h3><i class="fas fa-upload"></i> 图片上传</h3>
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <h4>拖拽图片或点击上传</h4>
                    <p>支持 JPG, PNG, GIF 格式</p>
                    <input type="file" id="fileInput" accept="image/*" style="display: none;">
                </div>
            </div>

            <!-- 查看模式 -->
            <div class="panel-section">
                <h3><i class="fas fa-eye"></i> 查看模式</h3>
                <div class="mode-buttons">
                    <div class="mode-btn active" data-mode="3d">
                        <i class="fas fa-cube"></i><br>3D模型
                    </div>
                    <div class="mode-btn" data-mode="panorama">
                        <i class="fas fa-globe"></i><br>全景图
                    </div>
                    <div class="mode-btn" data-mode="stereo">
                        <i class="fas fa-glasses"></i><br>立体视图
                    </div>
                    <div class="mode-btn" data-mode="wireframe">
                        <i class="fas fa-project-diagram"></i><br>线框模式
                    </div>
                </div>
            </div>

            <!-- 3D控制 -->
            <div class="panel-section">
                <h3><i class="fas fa-sliders-h"></i> 3D控制</h3>

                <div class="control-group">
                    <label>深度强度</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="depthSlider" min="0" max="1" step="0.1" value="0.5">
                        <span class="slider-value" id="depthValue">0.5</span>
                    </div>
                </div>

                <div class="control-group">
                    <label>旋转速度</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="rotationSpeedSlider" min="0" max="5" step="0.1" value="1">
                        <span class="slider-value" id="rotationSpeedValue">1.0</span>
                    </div>
                </div>

                <div class="control-group">
                    <label>缩放</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="scaleSlider" min="0.1" max="3" step="0.1" value="1">
                        <span class="slider-value" id="scaleValue">1.0</span>
                    </div>
                </div>

                <div class="control-group">
                    <label>
                        <input type="checkbox" id="autoRotateCheckbox" checked> 自动旋转
                    </label>
                </div>
            </div>

            <!-- 光照控制 -->
            <div class="panel-section">
                <h3><i class="fas fa-lightbulb"></i> 光照设置</h3>

                <div class="control-group">
                    <label>环境光强度</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="ambientLightSlider" min="0" max="1" step="0.1" value="0.3">
                        <span class="slider-value" id="ambientLightValue">0.3</span>
                    </div>
                </div>

                <div class="control-group">
                    <label>方向光强度</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="directionalLightSlider" min="0" max="2" step="0.1" value="0.7">
                        <span class="slider-value" id="directionalLightValue">0.7</span>
                    </div>
                </div>

                <div class="control-group">
                    <label>光源位置X</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="lightPositionXSlider" min="-5" max="5" step="0.1" value="1">
                        <span class="slider-value" id="lightPositionXValue">1.0</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="panel-section">
                <h3><i class="fas fa-tools"></i> 操作</h3>
                <div class="button-group">
                    <button class="btn btn-primary" id="generateBtn">
                        <i class="fas fa-magic"></i> 生成3D
                    </button>
                    <button class="btn btn-secondary" id="resetBtn">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                    <button class="btn btn-success" id="saveBtn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn btn-danger" id="exportBtn">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>

            <!-- 预设效果 -->
            <div class="panel-section">
                <h3><i class="fas fa-palette"></i> 预设效果</h3>
                <div class="button-group">
                    <button class="btn btn-secondary" data-preset="low">低深度</button>
                    <button class="btn btn-secondary" data-preset="medium">中深度</button>
                    <button class="btn btn-secondary" data-preset="high">高深度</button>
                    <button class="btn btn-secondary" data-preset="extreme">极深度</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ThreeDViewer {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.currentMesh = null;
                this.animationId = null;
                this.currentImage = null;
                this.currentMode = '3d';

                this.init();
                this.setupEventListeners();
            }

            init() {
                // 初始化Three.js场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x1a1a1a);

                // 初始化相机
                const canvas = document.getElementById('threejs-canvas');
                const aspect = canvas.clientWidth / canvas.clientHeight;
                this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
                this.camera.position.set(0, 0, 5);

                // 初始化渲染器
                this.renderer = new THREE.WebGLRenderer({
                    canvas: canvas,
                    antialias: true,
                    alpha: true
                });
                this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
                this.renderer.setPixelRatio(window.devicePixelRatio);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

                // 初始化控制器
                this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                this.controls.autoRotate = true;
                this.controls.autoRotateSpeed = 1.0;

                // 添加光照
                this.setupLighting();

                // 开始渲染循环
                this.animate();

                // 处理窗口大小变化
                window.addEventListener('resize', () => this.onWindowResize());
            }

            setupLighting() {
                // 环境光
                this.ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
                this.scene.add(this.ambientLight);

                // 方向光
                this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.7);
                this.directionalLight.position.set(1, 1, 1);
                this.directionalLight.castShadow = true;
                this.directionalLight.shadow.mapSize.width = 2048;
                this.directionalLight.shadow.mapSize.height = 2048;
                this.scene.add(this.directionalLight);

                // 点光源
                this.pointLight = new THREE.PointLight(0x64b5f6, 0.5, 100);
                this.pointLight.position.set(-1, 1, 1);
                this.scene.add(this.pointLight);
            }

            setupEventListeners() {
                // 文件上传
                const fileInput = document.getElementById('fileInput');
                const uploadArea = document.getElementById('uploadArea');

                uploadArea.addEventListener('click', () => fileInput.click());
                uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
                uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
                fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

                // 模式切换
                document.querySelectorAll('.mode-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => this.switchMode(e.target.closest('.mode-btn').dataset.mode));
                });

                // 滑块控制
                this.setupSliderControls();

                // 按钮事件
                document.getElementById('generateBtn').addEventListener('click', () => this.generate3DModel());
                document.getElementById('resetBtn').addEventListener('click', () => this.resetScene());
                document.getElementById('saveBtn').addEventListener('click', () => this.saveModel());
                document.getElementById('exportBtn').addEventListener('click', () => this.exportModel());
                document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());

                // 预设效果
                document.querySelectorAll('[data-preset]').forEach(btn => {
                    btn.addEventListener('click', (e) => this.applyPreset(e.target.dataset.preset));
                });

                // 自动旋转复选框
                document.getElementById('autoRotateCheckbox').addEventListener('change', (e) => {
                    this.controls.autoRotate = e.target.checked;
                });
            }

            setupSliderControls() {
                const sliders = [
                    { id: 'depthSlider', valueId: 'depthValue', callback: (value) => this.updateDepth(value) },
                    { id: 'rotationSpeedSlider', valueId: 'rotationSpeedValue', callback: (value) => this.updateRotationSpeed(value) },
                    { id: 'scaleSlider', valueId: 'scaleValue', callback: (value) => this.updateScale(value) },
                    { id: 'ambientLightSlider', valueId: 'ambientLightValue', callback: (value) => this.updateAmbientLight(value) },
                    { id: 'directionalLightSlider', valueId: 'directionalLightValue', callback: (value) => this.updateDirectionalLight(value) },
                    { id: 'lightPositionXSlider', valueId: 'lightPositionXValue', callback: (value) => this.updateLightPosition(value) }
                ];

                sliders.forEach(slider => {
                    const element = document.getElementById(slider.id);
                    const valueElement = document.getElementById(slider.valueId);

                    element.addEventListener('input', (e) => {
                        const value = parseFloat(e.target.value);
                        valueElement.textContent = value.toFixed(1);
                        slider.callback(value);
                    });
                });
            }

            handleDragOver(event) {
                event.preventDefault();
                event.currentTarget.style.borderColor = '#64b5f6';
            }

            handleDrop(event) {
                event.preventDefault();
                event.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';

                const files = event.dataTransfer.files;
                if (files.length > 0) {
                    this.loadImage(files[0]);
                }
            }

            handleFileSelect(event) {
                const files = event.target.files;
                if (files.length > 0) {
                    this.loadImage(files[0]);
                }
            }

            loadImage(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.currentImage = e.target.result;
                    this.showNotification('图片加载成功！', 'success');

                    // 创建简单的平面预览
                    this.createImagePlane(e.target.result);
                };
                reader.readAsDataURL(file);
            }

            createImagePlane(imageData) {
                // 移除现有网格
                if (this.currentMesh) {
                    this.scene.remove(this.currentMesh);
                }

                // 创建纹理
                const loader = new THREE.TextureLoader();
                loader.load(imageData, (texture) => {
                    // 创建平面几何体
                    const geometry = new THREE.PlaneGeometry(4, 3);
                    const material = new THREE.MeshLambertMaterial({ map: texture });

                    this.currentMesh = new THREE.Mesh(geometry, material);
                    this.currentMesh.receiveShadow = true;
                    this.scene.add(this.currentMesh);

                    this.updateInfoPanel(geometry);
                });
            }

            generate3DModel() {
                if (!this.currentImage) {
                    this.showNotification('请先上传图片！', 'error');
                    return;
                }

                this.showLoading(true);

                // 模拟API调用
                setTimeout(() => {
                    this.create3DModel();
                    this.showLoading(false);
                    this.showNotification('3D模型生成成功！', 'success');
                }, 2000);
            }

            create3DModel() {
                if (!this.currentMesh) return;

                // 移除现有网格
                this.scene.remove(this.currentMesh);

                // 创建3D几何体（简化的立方体示例）
                const geometry = new THREE.BoxGeometry(2, 2, 2);
                const material = this.currentMesh.material.clone();

                this.currentMesh = new THREE.Mesh(geometry, material);
                this.currentMesh.castShadow = true;
                this.currentMesh.receiveShadow = true;
                this.scene.add(this.currentMesh);

                this.updateInfoPanel(geometry);
            }

            switchMode(mode) {
                this.currentMode = mode;

                // 更新按钮状态
                document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
                document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

                // 更新信息面板
                document.getElementById('currentMode').textContent = this.getModeDisplayName(mode);

                this.showNotification(`切换到${this.getModeDisplayName(mode)}模式`, 'info');
            }

            getModeDisplayName(mode) {
                const names = {
                    '3d': '3D模型',
                    'panorama': '全景图',
                    'stereo': '立体视图',
                    'wireframe': '线框模式'
                };
                return names[mode] || mode;
            }

            updateDepth(value) {
                if (this.currentMesh && this.currentMesh.geometry) {
                    // 更新深度效果
                    this.currentMesh.scale.z = value * 2;
                }
            }

            updateRotationSpeed(value) {
                this.controls.autoRotateSpeed = value;
            }

            updateScale(value) {
                if (this.currentMesh) {
                    this.currentMesh.scale.setScalar(value);
                }
            }

            updateAmbientLight(value) {
                this.ambientLight.intensity = value;
            }

            updateDirectionalLight(value) {
                this.directionalLight.intensity = value;
            }

            updateLightPosition(value) {
                this.directionalLight.position.x = value;
            }

            applyPreset(preset) {
                const presets = {
                    'low': { depth: 0.2, rotation: 0.5, scale: 0.8 },
                    'medium': { depth: 0.5, rotation: 1.0, scale: 1.0 },
                    'high': { depth: 0.8, rotation: 1.5, scale: 1.2 },
                    'extreme': { depth: 1.0, rotation: 2.0, scale: 1.5 }
                };

                const config = presets[preset];
                if (config) {
                    document.getElementById('depthSlider').value = config.depth;
                    document.getElementById('rotationSpeedSlider').value = config.rotation;
                    document.getElementById('scaleSlider').value = config.scale;

                    document.getElementById('depthValue').textContent = config.depth.toFixed(1);
                    document.getElementById('rotationSpeedValue').textContent = config.rotation.toFixed(1);
                    document.getElementById('scaleValue').textContent = config.scale.toFixed(1);

                    this.updateDepth(config.depth);
                    this.updateRotationSpeed(config.rotation);
                    this.updateScale(config.scale);

                    this.showNotification(`应用${preset}预设`, 'success');
                }
            }

            resetScene() {
                if (this.currentMesh) {
                    this.scene.remove(this.currentMesh);
                    this.currentMesh = null;
                }

                // 重置控制器
                this.camera.position.set(0, 0, 5);
                this.controls.reset();

                // 重置滑块
                document.getElementById('depthSlider').value = 0.5;
                document.getElementById('rotationSpeedSlider').value = 1.0;
                document.getElementById('scaleSlider').value = 1.0;

                this.showNotification('场景已重置', 'info');
            }

            saveModel() {
                this.showNotification('模型保存功能开发中...', 'info');
            }

            exportModel() {
                this.showNotification('模型导出功能开发中...', 'info');
            }

            toggleFullscreen() {
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen();
                    document.getElementById('fullscreenBtn').innerHTML = '<i class="fas fa-compress"></i>';
                } else {
                    document.exitFullscreen();
                    document.getElementById('fullscreenBtn').innerHTML = '<i class="fas fa-expand"></i>';
                }
            }

            updateInfoPanel(geometry) {
                if (geometry) {
                    const vertices = geometry.attributes.position ? geometry.attributes.position.count : 0;
                    const faces = geometry.index ? geometry.index.count / 3 : vertices / 3;

                    document.getElementById('vertexCount').textContent = vertices;
                    document.getElementById('faceCount').textContent = Math.floor(faces);
                }
            }

            showLoading(show) {
                document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                this.controls.update();
                this.renderer.render(this.scene, this.camera);

                // 更新FPS计数器
                this.updateFPS();
            }

            updateFPS() {
                // 简化的FPS计算
                const fps = Math.round(1000 / 16.67); // 假设60fps
                document.getElementById('fpsCounter').textContent = fps;
            }

            onWindowResize() {
                const canvas = document.getElementById('threejs-canvas');
                const aspect = canvas.clientWidth / canvas.clientHeight;

                this.camera.aspect = aspect;
                this.camera.updateProjectionMatrix();

                this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            }

            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }
            }
        }

        // 初始化3D查看器
        document.addEventListener('DOMContentLoaded', () => {
            new ThreeDViewer();
        });
    </script>
</body>
</html>