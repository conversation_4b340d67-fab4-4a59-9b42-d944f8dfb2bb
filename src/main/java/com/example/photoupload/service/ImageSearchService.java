package com.example.photoupload.service;

import com.example.photoupload.dto.ImageSearchRequest;
import com.example.photoupload.dto.ImageSearchResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 图像搜索服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface ImageSearchService {

    /**
     * 以图搜图
     * 
     * @param file 上传的图片文件
     * @param request 搜索请求
     * @return 搜索响应
     */
    ImageSearchResponse searchByImage(MultipartFile file, ImageSearchRequest request);

    /**
     * 语义搜索
     * 
     * @param request 搜索请求
     * @return 搜索响应
     */
    ImageSearchResponse semanticSearch(ImageSearchRequest request);

    /**
     * 高级搜索
     * 
     * @param request 搜索请求
     * @return 搜索响应
     */
    ImageSearchResponse advancedSearch(ImageSearchRequest request);

    /**
     * 颜色搜索
     * 
     * @param request 搜索请求
     * @return 搜索响应
     */
    ImageSearchResponse searchByColor(ImageSearchRequest request);

    /**
     * 人脸搜索
     * 
     * @param file 上传的图片文件
     * @param request 搜索请求
     * @return 搜索响应
     */
    ImageSearchResponse searchByFace(MultipartFile file, ImageSearchRequest request);

    /**
     * 物体搜索
     * 
     * @param request 搜索请求
     * @return 搜索响应
     */
    ImageSearchResponse searchByObject(ImageSearchRequest request);

    /**
     * 场景搜索
     * 
     * @param request 搜索请求
     * @return 搜索响应
     */
    ImageSearchResponse searchByScene(ImageSearchRequest request);

    /**
     * 获取搜索建议
     * 
     * @param query 查询关键词
     * @param limit 返回数量限制
     * @return 搜索建议列表
     */
    List<String> getSearchSuggestions(String query, int limit);

    /**
     * 获取热门搜索
     * 
     * @param limit 返回数量限制
     * @return 热门搜索列表
     */
    List<String> getTrendingSearches(int limit);

    /**
     * 获取搜索历史
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 搜索历史列表
     */
    List<ImageSearchResponse.SearchHistory> getSearchHistory(String userId, int page, int size);

    /**
     * 清除搜索历史
     * 
     * @param userId 用户ID
     * @return 清除响应
     */
    ImageSearchResponse clearSearchHistory(String userId);

    /**
     * 保存搜索
     * 
     * @param request 搜索请求
     * @return 保存响应
     */
    ImageSearchResponse saveSearch(ImageSearchRequest request);

    /**
     * 获取保存的搜索
     * 
     * @param userId 用户ID
     * @return 保存的搜索列表
     */
    List<ImageSearchResponse.SavedSearch> getSavedSearches(String userId);

    /**
     * 删除保存的搜索
     * 
     * @param userId 用户ID
     * @param searchId 搜索ID
     * @return 删除响应
     */
    ImageSearchResponse deleteSavedSearch(String userId, String searchId);

    /**
     * 获取搜索统计
     * 
     * @return 搜索统计响应
     */
    ImageSearchResponse getSearchStats();

    /**
     * 重建搜索索引
     * 
     * @return 重建响应
     */
    ImageSearchResponse rebuildSearchIndex();

    /**
     * 添加图片到搜索索引
     * 
     * @param imageId 图片ID
     * @param imagePath 图片路径
     * @return 添加响应
     */
    ImageSearchResponse addToSearchIndex(String imageId, String imagePath);

    /**
     * 从搜索索引中移除图片
     * 
     * @param imageId 图片ID
     * @return 移除响应
     */
    ImageSearchResponse removeFromSearchIndex(String imageId);

    /**
     * 更新搜索索引
     * 
     * @param imageId 图片ID
     * @param imagePath 图片路径
     * @return 更新响应
     */
    ImageSearchResponse updateSearchIndex(String imageId, String imagePath);

    /**
     * 获取索引状态
     * 
     * @return 索引状态响应
     */
    ImageSearchResponse getIndexStatus();

    /**
     * 优化搜索索引
     * 
     * @return 优化响应
     */
    ImageSearchResponse optimizeSearchIndex();

    /**
     * 批量搜索
     * 
     * @param requests 搜索请求列表
     * @return 搜索响应列表
     */
    List<ImageSearchResponse> batchSearch(List<ImageSearchRequest> requests);

    /**
     * 相似图片推荐
     * 
     * @param imageId 基准图片ID
     * @param limit 推荐数量
     * @return 推荐图片列表
     */
    List<ImageSearchResponse.SearchResult> getSimilarImages(String imageId, int limit);

    /**
     * 基于用户行为的个性化搜索
     * 
     * @param userId 用户ID
     * @param request 搜索请求
     * @return 个性化搜索响应
     */
    ImageSearchResponse personalizedSearch(String userId, ImageSearchRequest request);

    /**
     * 搜索结果排序
     * 
     * @param results 原始搜索结果
     * @param sortBy 排序字段
     * @param sortOrder 排序顺序
     * @return 排序后的结果
     */
    List<ImageSearchResponse.SearchResult> sortSearchResults(
            List<ImageSearchResponse.SearchResult> results, String sortBy, String sortOrder);

    /**
     * 搜索结果过滤
     * 
     * @param results 原始搜索结果
     * @param filters 过滤条件
     * @return 过滤后的结果
     */
    List<ImageSearchResponse.SearchResult> filterSearchResults(
            List<ImageSearchResponse.SearchResult> results, ImageSearchRequest.SearchFilters filters);

    /**
     * 计算图片相似度
     * 
     * @param imageId1 图片1 ID
     * @param imageId2 图片2 ID
     * @return 相似度分数
     */
    double calculateImageSimilarity(String imageId, String imageId2);

    /**
     * 提取图片特征向量
     * 
     * @param imagePath 图片路径
     * @return 特征向量
     */
    List<Double> extractImageFeatures(String imagePath);

    /**
     * 生成图片指纹
     * 
     * @param imagePath 图片路径
     * @return 图片指纹
     */
    String generateImageFingerprint(String imagePath);

    /**
     * 检测重复图片
     * 
     * @param imagePath 图片路径
     * @param threshold 相似度阈值
     * @return 重复图片列表
     */
    List<String> detectDuplicateImages(String imagePath, double threshold);

    /**
     * 搜索性能分析
     * 
     * @param searchId 搜索ID
     * @return 性能分析结果
     */
    ImageSearchResponse analyzeSearchPerformance(String searchId);

    /**
     * 搜索质量评估
     * 
     * @param searchId 搜索ID
     * @param userFeedback 用户反馈
     * @return 质量评估结果
     */
    ImageSearchResponse evaluateSearchQuality(String searchId, Map<String, Object> userFeedback);

    /**
     * 导出搜索数据
     * 
     * @param userId 用户ID
     * @param format 导出格式
     * @return 导出数据
     */
    String exportSearchData(String userId, String format);

    /**
     * 导入搜索数据
     * 
     * @param userId 用户ID
     * @param data 导入数据
     * @return 导入响应
     */
    ImageSearchResponse importSearchData(String userId, String data);

    /**
     * 搜索缓存管理
     * 
     * @param action 操作类型（clear, refresh, status）
     * @return 缓存管理响应
     */
    ImageSearchResponse manageSearchCache(String action);

    /**
     * 获取搜索配置
     * 
     * @return 搜索配置
     */
    Map<String, Object> getSearchConfiguration();

    /**
     * 更新搜索配置
     * 
     * @param configuration 新配置
     * @return 更新响应
     */
    ImageSearchResponse updateSearchConfiguration(Map<String, Object> configuration);

    /**
     * 搜索健康检查
     * 
     * @return 健康检查结果
     */
    ImageSearchResponse healthCheck();

    /**
     * 搜索引擎监控
     * 
     * @return 监控数据
     */
    Map<String, Object> getSearchMetrics();
}
