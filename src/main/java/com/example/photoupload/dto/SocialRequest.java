package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * 社交功能请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "社交功能请求")
public class SocialRequest {

    @Schema(description = "用户ID", example = "user_123")
    @JsonProperty("userId")
    private String userId;

    @Schema(description = "图片ID", example = "img_456")
    @JsonProperty("imageId")
    private String imageId;

    @Schema(description = "目标用户ID", example = "user_789")
    @JsonProperty("targetUserId")
    private String targetUserId;

    @Schema(description = "平台类型", example = "weibo")
    @JsonProperty("platform")
    private String platform;

    @Schema(description = "分享标题")
    @JsonProperty("title")
    private String title;

    @Schema(description = "分享描述")
    @JsonProperty("description")
    private String description;

    @Schema(description = "评论内容")
    @JsonProperty("comment")
    private String comment;

    @Schema(description = "评论ID", example = "comment_123")
    @JsonProperty("commentId")
    private String commentId;

    @Schema(description = "操作类型", example = "like")
    @JsonProperty("action")
    private String action;

    @Schema(description = "内容ID")
    @JsonProperty("contentId")
    private String contentId;

    @Schema(description = "内容类型", example = "image")
    @JsonProperty("contentType")
    private String contentType;

    @Schema(description = "举报原因")
    @JsonProperty("reason")
    private String reason;

    @Schema(description = "标签列表")
    @JsonProperty("tags")
    private List<String> tags;

    @Schema(description = "分享设置")
    @JsonProperty("shareSettings")
    private ShareSettings shareSettings;

    @Schema(description = "隐私设置", example = "public")
    @JsonProperty("privacy")
    private String privacy;

    @Schema(description = "位置信息")
    @JsonProperty("location")
    private LocationInfo location;

    @Schema(description = "提及的用户")
    @JsonProperty("mentions")
    private List<String> mentions;

    @Schema(description = "额外参数")
    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    @Schema(description = "是否自动发布", example = "true")
    @JsonProperty("autoPublish")
    private boolean autoPublish = true;

    @Schema(description = "定时发布时间")
    @JsonProperty("scheduledTime")
    private String scheduledTime;

    // 构造函数
    public SocialRequest() {
        this.shareSettings = new ShareSettings();
    }

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getTargetUserId() {
        return targetUserId;
    }

    public void setTargetUserId(String targetUserId) {
        this.targetUserId = targetUserId;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getCommentId() {
        return commentId;
    }

    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public ShareSettings getShareSettings() {
        return shareSettings;
    }

    public void setShareSettings(ShareSettings shareSettings) {
        this.shareSettings = shareSettings;
    }

    public String getPrivacy() {
        return privacy;
    }

    public void setPrivacy(String privacy) {
        this.privacy = privacy;
    }

    public LocationInfo getLocation() {
        return location;
    }

    public void setLocation(LocationInfo location) {
        this.location = location;
    }

    public List<String> getMentions() {
        return mentions;
    }

    public void setMentions(List<String> mentions) {
        this.mentions = mentions;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public boolean isAutoPublish() {
        return autoPublish;
    }

    public void setAutoPublish(boolean autoPublish) {
        this.autoPublish = autoPublish;
    }

    public String getScheduledTime() {
        return scheduledTime;
    }

    public void setScheduledTime(String scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    /**
     * 分享设置内部类
     */
    @Schema(description = "分享设置")
    public static class ShareSettings {
        @Schema(description = "包含原图", example = "true")
        private boolean includeOriginalImage = true;

        @Schema(description = "添加水印", example = "false")
        private boolean addWatermark = false;

        @Schema(description = "压缩质量", example = "80")
        private int compressionQuality = 80;

        @Schema(description = "最大尺寸", example = "1080")
        private int maxSize = 1080;

        @Schema(description = "自动添加标签", example = "true")
        private boolean autoAddTags = true;

        @Schema(description = "同步到多平台", example = "false")
        private boolean syncToMultiplePlatforms = false;

        @Schema(description = "启用评论", example = "true")
        private boolean enableComments = true;

        @Schema(description = "启用点赞", example = "true")
        private boolean enableLikes = true;

        @Schema(description = "自定义缩略图")
        private String customThumbnail;

        // Getters and Setters
        public boolean isIncludeOriginalImage() { return includeOriginalImage; }
        public void setIncludeOriginalImage(boolean includeOriginalImage) { this.includeOriginalImage = includeOriginalImage; }

        public boolean isAddWatermark() { return addWatermark; }
        public void setAddWatermark(boolean addWatermark) { this.addWatermark = addWatermark; }

        public int getCompressionQuality() { return compressionQuality; }
        public void setCompressionQuality(int compressionQuality) { this.compressionQuality = compressionQuality; }

        public int getMaxSize() { return maxSize; }
        public void setMaxSize(int maxSize) { this.maxSize = maxSize; }

        public boolean isAutoAddTags() { return autoAddTags; }
        public void setAutoAddTags(boolean autoAddTags) { this.autoAddTags = autoAddTags; }

        public boolean isSyncToMultiplePlatforms() { return syncToMultiplePlatforms; }
        public void setSyncToMultiplePlatforms(boolean syncToMultiplePlatforms) { this.syncToMultiplePlatforms = syncToMultiplePlatforms; }

        public boolean isEnableComments() { return enableComments; }
        public void setEnableComments(boolean enableComments) { this.enableComments = enableComments; }

        public boolean isEnableLikes() { return enableLikes; }
        public void setEnableLikes(boolean enableLikes) { this.enableLikes = enableLikes; }

        public String getCustomThumbnail() { return customThumbnail; }
        public void setCustomThumbnail(String customThumbnail) { this.customThumbnail = customThumbnail; }
    }

    /**
     * 位置信息内部类
     */
    @Schema(description = "位置信息")
    public static class LocationInfo {
        @Schema(description = "纬度", example = "39.9042")
        private double latitude;

        @Schema(description = "经度", example = "116.4074")
        private double longitude;

        @Schema(description = "地址名称", example = "北京市")
        private String address;

        @Schema(description = "城市", example = "北京")
        private String city;

        @Schema(description = "国家", example = "中国")
        private String country;

        @Schema(description = "是否公开位置", example = "false")
        private boolean publicLocation = false;

        // Getters and Setters
        public double getLatitude() { return latitude; }
        public void setLatitude(double latitude) { this.latitude = latitude; }

        public double getLongitude() { return longitude; }
        public void setLongitude(double longitude) { this.longitude = longitude; }

        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }

        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }

        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }

        public boolean isPublicLocation() { return publicLocation; }
        public void setPublicLocation(boolean publicLocation) { this.publicLocation = publicLocation; }
    }
}
