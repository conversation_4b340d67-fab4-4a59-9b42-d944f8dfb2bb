package com.example.photoupload.service;

import com.example.photoupload.dto.SocialRequest;
import com.example.photoupload.dto.SocialResponse;

import java.util.List;

/**
 * 社交功能服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface SocialService {

    /**
     * 分享图片到社交媒体
     * 
     * @param request 分享请求
     * @return 分享响应
     */
    SocialResponse shareImage(SocialRequest request);

    /**
     * 点赞图片
     * 
     * @param imageId 图片ID
     * @param userId 用户ID
     * @return 点赞响应
     */
    SocialResponse likeImage(String imageId, String userId);

    /**
     * 取消点赞
     * 
     * @param imageId 图片ID
     * @param userId 用户ID
     * @return 取消点赞响应
     */
    SocialResponse unlikeImage(String imageId, String userId);

    /**
     * 添加评论
     * 
     * @param request 评论请求
     * @return 评论响应
     */
    SocialResponse addComment(SocialRequest request);

    /**
     * 获取图片评论
     * 
     * @param imageId 图片ID
     * @param page 页码
     * @param size 每页大小
     * @return 评论列表
     */
    List<SocialResponse.Comment> getComments(String imageId, int page, int size);

    /**
     * 删除评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 删除响应
     */
    SocialResponse deleteComment(String commentId, String userId);

    /**
     * 关注用户
     * 
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 关注响应
     */
    SocialResponse followUser(String userId, String targetUserId);

    /**
     * 取消关注
     * 
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 取消关注响应
     */
    SocialResponse unfollowUser(String userId, String targetUserId);

    /**
     * 获取用户动态
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 动态列表
     */
    List<SocialResponse.FeedItem> getUserFeed(String userId, int page, int size);

    /**
     * 获取热门图片
     * 
     * @param page 页码
     * @param size 每页大小
     * @param timeRange 时间范围
     * @return 热门图片列表
     */
    List<SocialResponse.TrendingImage> getTrendingImages(int page, int size, String timeRange);

    /**
     * 获取用户统计信息
     * 
     * @param userId 用户ID
     * @return 用户统计信息
     */
    SocialResponse.UserStats getUserStats(String userId);

    /**
     * 举报内容
     * 
     * @param request 举报请求
     * @return 举报响应
     */
    SocialResponse reportContent(SocialRequest request);

    /**
     * 生成分享链接
     * 
     * @param imageId 图片ID
     * @return 分享链接响应
     */
    SocialResponse generateShareLink(String imageId);

    /**
     * 批量操作
     * 
     * @param requests 请求列表
     * @return 响应列表
     */
    List<SocialResponse> batchOperations(List<SocialRequest> requests);

    /**
     * 获取关注列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 关注用户列表
     */
    List<SocialResponse.UserStats> getFollowingList(String userId, int page, int size);

    /**
     * 获取粉丝列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 粉丝用户列表
     */
    List<SocialResponse.UserStats> getFollowersList(String userId, int page, int size);

    /**
     * 搜索用户
     * 
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @return 用户列表
     */
    List<SocialResponse.UserStats> searchUsers(String keyword, int page, int size);

    /**
     * 获取推荐用户
     * 
     * @param userId 当前用户ID
     * @param count 推荐数量
     * @return 推荐用户列表
     */
    List<SocialResponse.UserStats> getRecommendedUsers(String userId, int count);

    /**
     * 获取图片互动统计
     * 
     * @param imageId 图片ID
     * @return 互动统计信息
     */
    SocialResponse getImageInteractionStats(String imageId);

    /**
     * 设置隐私权限
     * 
     * @param userId 用户ID
     * @param settings 隐私设置
     * @return 设置响应
     */
    SocialResponse updatePrivacySettings(String userId, SocialRequest.ShareSettings settings);

    /**
     * 获取通知列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 通知列表
     */
    List<SocialResponse> getNotifications(String userId, int page, int size);

    /**
     * 标记通知为已读
     * 
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 标记响应
     */
    SocialResponse markNotificationsAsRead(String userId, List<String> notificationIds);

    /**
     * 获取社交活动统计
     * 
     * @param userId 用户ID
     * @param timeRange 时间范围
     * @return 活动统计
     */
    SocialResponse getSocialActivityStats(String userId, String timeRange);

    /**
     * 创建话题标签
     * 
     * @param tag 标签名称
     * @param description 标签描述
     * @return 创建响应
     */
    SocialResponse createHashtag(String tag, String description);

    /**
     * 获取热门话题
     * 
     * @param count 数量
     * @return 热门话题列表
     */
    List<String> getTrendingHashtags(int count);

    /**
     * 根据话题获取图片
     * 
     * @param hashtag 话题标签
     * @param page 页码
     * @param size 每页大小
     * @return 图片列表
     */
    List<SocialResponse.TrendingImage> getImagesByHashtag(String hashtag, int page, int size);

    /**
     * 屏蔽用户
     * 
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 屏蔽响应
     */
    SocialResponse blockUser(String userId, String targetUserId);

    /**
     * 取消屏蔽用户
     * 
     * @param userId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 取消屏蔽响应
     */
    SocialResponse unblockUser(String userId, String targetUserId);

    /**
     * 获取屏蔽列表
     * 
     * @param userId 用户ID
     * @return 屏蔽用户列表
     */
    List<String> getBlockedUsers(String userId);

    /**
     * 验证用户权限
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @param targetId 目标ID
     * @return 是否有权限
     */
    boolean hasPermission(String userId, String action, String targetId);

    /**
     * 清理过期数据
     * 
     * @param daysOld 过期天数
     * @return 清理数量
     */
    int cleanupExpiredData(int daysOld);

    /**
     * 导出用户社交数据
     * 
     * @param userId 用户ID
     * @return 导出数据
     */
    String exportUserSocialData(String userId);

    /**
     * 计算用户影响力分数
     * 
     * @param userId 用户ID
     * @return 影响力分数
     */
    double calculateInfluenceScore(String userId);

    /**
     * 获取相似用户推荐
     * 
     * @param userId 用户ID
     * @param count 推荐数量
     * @return 相似用户列表
     */
    List<SocialResponse.UserStats> getSimilarUsers(String userId, int count);
}
