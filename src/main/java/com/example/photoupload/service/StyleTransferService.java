package com.example.photoupload.service;

import com.example.photoupload.dto.StyleTransferRequest;
import com.example.photoupload.dto.StyleTransferResponse;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 风格迁移服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface StyleTransferService {

    /**
     * 应用风格迁移
     * 
     * @param request 风格迁移请求
     * @return 风格迁移响应
     */
    StyleTransferResponse applyStyleTransfer(StyleTransferRequest request);

    /**
     * 上传自定义风格
     * 
     * @param file 风格图片文件
     * @param styleName 风格名称
     * @param description 风格描述
     * @return 上传响应
     */
    StyleTransferResponse uploadCustomStyle(MultipartFile file, String styleName, String description);

    /**
     * 获取可用风格列表
     * 
     * @return 风格列表
     */
    List<StyleTransferResponse.StyleInfo> getAvailableStyles();

    /**
     * 获取风格迁移历史
     * 
     * @return 历史记录列表
     */
    List<StyleTransferResponse> getTransferHistory();

    /**
     * 获取风格迁移结果文件
     * 
     * @param transferId 迁移任务ID
     * @return 结果文件资源
     */
    Resource getTransferResult(String transferId);

    /**
     * 批量风格迁移
     * 
     * @param requests 批量请求
     * @return 批量响应
     */
    List<StyleTransferResponse> batchStyleTransfer(List<StyleTransferRequest> requests);

    /**
     * 预览风格迁移效果
     * 
     * @param request 预览请求
     * @return 预览响应
     */
    StyleTransferResponse previewStyleTransfer(StyleTransferRequest request);

    /**
     * 获取风格迁移进度
     * 
     * @param transferId 迁移任务ID
     * @return 进度响应
     */
    StyleTransferResponse getTransferProgress(String transferId);

    /**
     * 取消风格迁移任务
     * 
     * @param transferId 迁移任务ID
     * @return 是否取消成功
     */
    boolean cancelTransfer(String transferId);

    /**
     * 删除风格迁移结果
     * 
     * @param transferId 迁移任务ID
     * @return 是否删除成功
     */
    boolean deleteTransferResult(String transferId);

    /**
     * 获取风格迁移统计信息
     * 
     * @return 统计信息响应
     */
    StyleTransferResponse getTransferStatistics();

    /**
     * 推荐适合的风格
     * 
     * @param imagePath 图片路径
     * @return 推荐风格列表
     */
    List<StyleTransferResponse.StyleInfo> recommendStyles(String imagePath);

    /**
     * 分析图片内容特征
     * 
     * @param imagePath 图片路径
     * @return 内容特征
     */
    StyleTransferResponse analyzeImageContent(String imagePath);

    /**
     * 验证风格迁移参数
     * 
     * @param request 请求参数
     * @return 是否有效
     */
    boolean validateTransferRequest(StyleTransferRequest request);

    /**
     * 优化风格迁移参数
     * 
     * @param request 原始请求
     * @return 优化后的请求
     */
    StyleTransferRequest optimizeTransferParameters(StyleTransferRequest request);

    /**
     * 生成风格迁移缩略图
     * 
     * @param transferId 迁移任务ID
     * @return 缩略图URL
     */
    String generateThumbnail(String transferId);

    /**
     * 计算风格相似度
     * 
     * @param image1Path 图片1路径
     * @param image2Path 图片2路径
     * @return 相似度分数
     */
    float calculateStyleSimilarity(String image1Path, String image2Path);

    /**
     * 获取风格迁移质量评分
     * 
     * @param originalPath 原图路径
     * @param styledPath 风格化图片路径
     * @return 质量评分
     */
    float evaluateTransferQuality(String originalPath, String styledPath);

    /**
     * 清理过期的迁移结果
     * 
     * @param daysOld 保留天数
     * @return 清理的文件数量
     */
    int cleanupExpiredResults(int daysOld);

    /**
     * 导出风格迁移配置
     * 
     * @param transferId 迁移任务ID
     * @return 配置JSON
     */
    String exportTransferConfig(String transferId);

    /**
     * 导入风格迁移配置
     * 
     * @param configJson 配置JSON
     * @return 导入的请求对象
     */
    StyleTransferRequest importTransferConfig(String configJson);
}
