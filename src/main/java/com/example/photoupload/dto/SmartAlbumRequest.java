package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * 智能相册请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "智能相册请求")
public class SmartAlbumRequest {

    @Schema(description = "相册名称", example = "我的家人")
    @JsonProperty("albumName")
    private String albumName;

    @Schema(description = "相册类型", example = "person")
    @JsonProperty("albumType")
    private String albumType;

    @Schema(description = "分类条件")
    @JsonProperty("criteria")
    private AlbumCriteria criteria;

    @Schema(description = "是否自动更新", example = "true")
    @JsonProperty("autoUpdate")
    private boolean autoUpdate = true;

    @Schema(description = "相册描述", example = "包含家人照片的智能相册")
    @JsonProperty("description")
    private String description;

    @Schema(description = "标签列表")
    @JsonProperty("tags")
    private List<String> tags;

    @Schema(description = "隐私设置", example = "private")
    @JsonProperty("privacy")
    private String privacy = "private";

    @Schema(description = "排序方式", example = "date_desc")
    @JsonProperty("sortBy")
    private String sortBy = "date_desc";

    @Schema(description = "最大图片数量", example = "1000")
    @JsonProperty("maxImages")
    private int maxImages = 1000;

    @Schema(description = "额外设置")
    @JsonProperty("settings")
    private Map<String, Object> settings;

    // 构造函数
    public SmartAlbumRequest() {
        this.criteria = new AlbumCriteria();
    }

    // Getters and Setters
    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public String getAlbumType() {
        return albumType;
    }

    public void setAlbumType(String albumType) {
        this.albumType = albumType;
    }

    public AlbumCriteria getCriteria() {
        return criteria;
    }

    public void setCriteria(AlbumCriteria criteria) {
        this.criteria = criteria;
    }

    public boolean isAutoUpdate() {
        return autoUpdate;
    }

    public void setAutoUpdate(boolean autoUpdate) {
        this.autoUpdate = autoUpdate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getPrivacy() {
        return privacy;
    }

    public void setPrivacy(String privacy) {
        this.privacy = privacy;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public int getMaxImages() {
        return maxImages;
    }

    public void setMaxImages(int maxImages) {
        this.maxImages = maxImages;
    }

    public Map<String, Object> getSettings() {
        return settings;
    }

    public void setSettings(Map<String, Object> settings) {
        this.settings = settings;
    }

    /**
     * 相册分类条件内部类
     */
    @Schema(description = "相册分类条件")
    public static class AlbumCriteria {
        @Schema(description = "人物名称列表")
        private List<String> personNames;

        @Schema(description = "场景类型列表")
        private List<String> sceneTypes;

        @Schema(description = "情感类型列表")
        private List<String> emotions;

        @Schema(description = "物体类型列表")
        private List<String> objectTypes;

        @Schema(description = "颜色主题列表")
        private List<String> colorThemes;

        @Schema(description = "时间范围")
        private TimeRange timeRange;

        @Schema(description = "地理位置")
        private LocationCriteria location;

        @Schema(description = "图片质量要求")
        private QualityCriteria quality;

        @Schema(description = "相似度阈值", example = "0.8")
        private float similarityThreshold = 0.8f;

        @Schema(description = "最小置信度", example = "0.7")
        private float minConfidence = 0.7f;

        // Getters and Setters
        public List<String> getPersonNames() { return personNames; }
        public void setPersonNames(List<String> personNames) { this.personNames = personNames; }

        public List<String> getSceneTypes() { return sceneTypes; }
        public void setSceneTypes(List<String> sceneTypes) { this.sceneTypes = sceneTypes; }

        public List<String> getEmotions() { return emotions; }
        public void setEmotions(List<String> emotions) { this.emotions = emotions; }

        public List<String> getObjectTypes() { return objectTypes; }
        public void setObjectTypes(List<String> objectTypes) { this.objectTypes = objectTypes; }

        public List<String> getColorThemes() { return colorThemes; }
        public void setColorThemes(List<String> colorThemes) { this.colorThemes = colorThemes; }

        public TimeRange getTimeRange() { return timeRange; }
        public void setTimeRange(TimeRange timeRange) { this.timeRange = timeRange; }

        public LocationCriteria getLocation() { return location; }
        public void setLocation(LocationCriteria location) { this.location = location; }

        public QualityCriteria getQuality() { return quality; }
        public void setQuality(QualityCriteria quality) { this.quality = quality; }

        public float getSimilarityThreshold() { return similarityThreshold; }
        public void setSimilarityThreshold(float similarityThreshold) { this.similarityThreshold = similarityThreshold; }

        public float getMinConfidence() { return minConfidence; }
        public void setMinConfidence(float minConfidence) { this.minConfidence = minConfidence; }
    }

    /**
     * 时间范围内部类
     */
    @Schema(description = "时间范围")
    public static class TimeRange {
        @Schema(description = "开始时间", example = "2023-01-01T00:00:00")
        private String startTime;

        @Schema(description = "结束时间", example = "2023-12-31T23:59:59")
        private String endTime;

        @Schema(description = "时间类型", example = "creation_time")
        private String timeType = "creation_time";

        // Getters and Setters
        public String getStartTime() { return startTime; }
        public void setStartTime(String startTime) { this.startTime = startTime; }

        public String getEndTime() { return endTime; }
        public void setEndTime(String endTime) { this.endTime = endTime; }

        public String getTimeType() { return timeType; }
        public void setTimeType(String timeType) { this.timeType = timeType; }
    }

    /**
     * 地理位置条件内部类
     */
    @Schema(description = "地理位置条件")
    public static class LocationCriteria {
        @Schema(description = "纬度", example = "39.9042")
        private double latitude;

        @Schema(description = "经度", example = "116.4074")
        private double longitude;

        @Schema(description = "半径(公里)", example = "10.0")
        private double radius;

        @Schema(description = "城市名称", example = "北京")
        private String city;

        @Schema(description = "国家名称", example = "中国")
        private String country;

        // Getters and Setters
        public double getLatitude() { return latitude; }
        public void setLatitude(double latitude) { this.latitude = latitude; }

        public double getLongitude() { return longitude; }
        public void setLongitude(double longitude) { this.longitude = longitude; }

        public double getRadius() { return radius; }
        public void setRadius(double radius) { this.radius = radius; }

        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }

        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
    }

    /**
     * 图片质量条件内部类
     */
    @Schema(description = "图片质量条件")
    public static class QualityCriteria {
        @Schema(description = "最小宽度", example = "800")
        private int minWidth;

        @Schema(description = "最小高度", example = "600")
        private int minHeight;

        @Schema(description = "最小文件大小(字节)", example = "100000")
        private long minFileSize;

        @Schema(description = "最大文件大小(字节)", example = "10000000")
        private long maxFileSize;

        @Schema(description = "最小清晰度分数", example = "0.7")
        private float minSharpness;

        @Schema(description = "最小亮度分数", example = "0.3")
        private float minBrightness;

        @Schema(description = "最大亮度分数", example = "0.9")
        private float maxBrightness;

        // Getters and Setters
        public int getMinWidth() { return minWidth; }
        public void setMinWidth(int minWidth) { this.minWidth = minWidth; }

        public int getMinHeight() { return minHeight; }
        public void setMinHeight(int minHeight) { this.minHeight = minHeight; }

        public long getMinFileSize() { return minFileSize; }
        public void setMinFileSize(long minFileSize) { this.minFileSize = minFileSize; }

        public long getMaxFileSize() { return maxFileSize; }
        public void setMaxFileSize(long maxFileSize) { this.maxFileSize = maxFileSize; }

        public float getMinSharpness() { return minSharpness; }
        public void setMinSharpness(float minSharpness) { this.minSharpness = minSharpness; }

        public float getMinBrightness() { return minBrightness; }
        public void setMinBrightness(float minBrightness) { this.minBrightness = minBrightness; }

        public float getMaxBrightness() { return maxBrightness; }
        public void setMaxBrightness(float maxBrightness) { this.maxBrightness = maxBrightness; }
    }
}
