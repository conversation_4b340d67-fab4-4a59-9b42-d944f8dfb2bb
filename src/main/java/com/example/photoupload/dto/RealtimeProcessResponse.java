package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 实时处理响应DTO
 * 
 * 用于封装实时图像处理的响应结果，包含处理状态、进度、结果数据等信息。
 * 支持异步处理场景，可以通过WebSocket或长轮询方式返回处理进度和结果。
 * 
 * 主要功能：
 * - 处理状态跟踪（处理中、成功、失败）
 * - 处理进度反馈
 * - 处理结果数据传输
 * - 错误信息记录
 * - 性能指标收集
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Schema(description = "实时处理响应")
public class RealtimeProcessResponse {

    /** 会话标识符，用于关联同一个处理会话的多个请求 */
    @Schema(description = "会话ID", example = "session_123456")
    @JsonProperty("sessionId")
    private String sessionId;

    /** 请求标识符，用于唯一标识单个处理请求 */
    @Schema(description = "请求ID", example = "req_123456")
    @JsonProperty("requestId")
    private String requestId;

    /** 处理状态：success(成功)、processing(处理中)、error(失败) */
    @Schema(description = "处理状态", example = "success")
    @JsonProperty("status")
    private String status;

    /** 响应消息，用于描述当前处理状态或结果 */
    @Schema(description = "响应消息", example = "处理成功")
    @JsonProperty("message")
    private String message;

    /** 处理后的图像数据，Base64编码格式 */
    @Schema(description = "处理后的图像Base64数据")
    @JsonProperty("processedImageData")
    private String processedImageData;

    /** 预览图像数据，Base64编码格式，通常是缩略图 */
    @Schema(description = "预览图像Base64数据")
    @JsonProperty("previewImageData")
    private String previewImageData;

    /** 处理进度，范围0-100 */
    @Schema(description = "处理进度", example = "85")
    @JsonProperty("progress")
    private Integer progress;

    /** 处理耗时，单位为毫秒 */
    @Schema(description = "处理时间(毫秒)", example = "1500")
    @JsonProperty("processingTime")
    private Long processingTime;

    /** 处理开始时间 */
    @Schema(description = "处理开始时间")
    @JsonProperty("startTime")
    private LocalDateTime startTime;

    /** 处理结束时间 */
    @Schema(description = "处理结束时间")
    @JsonProperty("endTime")
    private LocalDateTime endTime;

    /** 图像基本信息，包含尺寸、格式等 */
    @Schema(description = "图像信息")
    @JsonProperty("imageInfo")
    private ImageInfo imageInfo;

    /** 错误详细信息，当处理失败时提供具体错误描述 */
    @Schema(description = "错误详情")
    @JsonProperty("errorDetails")
    private String errorDetails;

    /** 扩展数据，用于传递额外的处理结果或元数据 */
    @Schema(description = "额外数据")
    @JsonProperty("extraData")
    private Map<String, Object> extraData;

    /**
     * 默认构造函数
     * 自动设置处理开始时间为当前时间
     */
    public RealtimeProcessResponse() {
        this.startTime = LocalDateTime.now();
    }

    /**
     * 创建成功响应的便捷方法
     * 
     * @param sessionId 会话ID
     * @param requestId 请求ID
     * @param message 成功消息
     * @return 配置好的成功响应对象
     */
    public static RealtimeProcessResponse success(String sessionId, String requestId, String message) {
        RealtimeProcessResponse response = new RealtimeProcessResponse();
        response.setSessionId(sessionId);
        response.setRequestId(requestId);
        response.setStatus("success");
        response.setMessage(message);
        response.setProgress(100);
        response.setEndTime(LocalDateTime.now());
        return response;
    }

    /**
     * 创建处理中响应的便捷方法
     * 
     * @param sessionId 会话ID
     * @param requestId 请求ID
     * @param message 处理消息
     * @param progress 当前进度(0-100)
     * @return 配置好的处理中响应对象
     */
    public static RealtimeProcessResponse processing(String sessionId, String requestId, String message, int progress) {
        RealtimeProcessResponse response = new RealtimeProcessResponse();
        response.setSessionId(sessionId);
        response.setRequestId(requestId);
        response.setStatus("processing");
        response.setMessage(message);
        response.setProgress(progress);
        return response;
    }

    /**
     * 创建错误响应的便捷方法
     * 
     * @param sessionId 会话ID
     * @param requestId 请求ID
     * @param message 错误消息
     * @param errorDetails 错误详细信息
     * @return 配置好的错误响应对象
     */
    public static RealtimeProcessResponse error(String sessionId, String requestId, String message, String errorDetails) {
        RealtimeProcessResponse response = new RealtimeProcessResponse();
        response.setSessionId(sessionId);
        response.setRequestId(requestId);
        response.setStatus("error");
        response.setMessage(message);
        response.setErrorDetails(errorDetails);
        response.setEndTime(LocalDateTime.now());
        return response;
    }

    // ================ Getter and Setter Methods ================

    /**
     * 获取会话ID
     * @return 会话ID
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * 设置会话ID
     * @param sessionId 会话ID
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    /**
     * 获取请求ID
     * @return 请求ID
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * 设置请求ID
     * @param requestId 请求ID
     */
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    /**
     * 获取处理状态
     * @return 处理状态
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置处理状态
     * @param status 处理状态(success/processing/error)
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取响应消息
     * @return 响应消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置响应消息
     * @param message 响应消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 获取处理后的图像数据
     * @return Base64编码的图像数据
     */
    public String getProcessedImageData() {
        return processedImageData;
    }

    /**
     * 设置处理后的图像数据
     * @param processedImageData Base64编码的图像数据
     */
    public void setProcessedImageData(String processedImageData) {
        this.processedImageData = processedImageData;
    }

    /**
     * 获取预览图像数据
     * @return Base64编码的预览图像数据
     */
    public String getPreviewImageData() {
        return previewImageData;
    }

    /**
     * 设置预览图像数据
     * @param previewImageData Base64编码的预览图像数据
     */
    public void setPreviewImageData(String previewImageData) {
        this.previewImageData = previewImageData;
    }

    /**
     * 获取处理进度
     * @return 处理进度(0-100)
     */
    public Integer getProgress() {
        return progress;
    }

    /**
     * 设置处理进度
     * @param progress 处理进度(0-100)
     */
    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    /**
     * 获取处理耗时
     * @return 处理耗时(毫秒)
     */
    public Long getProcessingTime() {
        return processingTime;
    }

    /**
     * 设置处理耗时
     * @param processingTime 处理耗时(毫秒)
     */
    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    /**
     * 获取处理开始时间
     * @return 处理开始时间
     */
    public LocalDateTime getStartTime() {
        return startTime;
    }

    /**
     * 设置处理开始时间
     * @param startTime 处理开始时间
     */
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取处理结束时间
     * @return 处理结束时间
     */
    public LocalDateTime getEndTime() {
        return endTime;
    }

    /**
     * 设置处理结束时间
     * @param endTime 处理结束时间
     */
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取图像信息
     * @return 图像信息对象
     */
    public ImageInfo getImageInfo() {
        return imageInfo;
    }

    /**
     * 设置图像信息
     * @param imageInfo 图像信息对象
     */
    public void setImageInfo(ImageInfo imageInfo) {
        this.imageInfo = imageInfo;
    }

    /**
     * 获取错误详细信息
     * @return 错误详细信息
     */
    public String getErrorDetails() {
        return errorDetails;
    }

    /**
     * 设置错误详细信息
     * @param errorDetails 错误详细信息
     */
    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }

    /**
     * 获取扩展数据
     * @return 扩展数据Map
     */
    public Map<String, Object> getExtraData() {
        return extraData;
    }

    /**
     * 设置扩展数据
     * @param extraData 扩展数据Map
     */
    public void setExtraData(Map<String, Object> extraData) {
        this.extraData = extraData;
    }

    /**
     * 图像信息内部类
     * 
     * 用于封装图像的基本属性信息，包括尺寸、格式、文件大小等。
     * 这些信息有助于客户端了解处理后图像的基本特征。
     */
    @Schema(description = "图像信息")
    public static class ImageInfo {
        /** 图像宽度，单位像素 */
        @Schema(description = "图像宽度", example = "1920")
        private int width;

        /** 图像高度，单位像素 */
        @Schema(description = "图像高度", example = "1080")
        private int height;

        /** 图像格式，如JPEG、PNG等 */
        @Schema(description = "图像格式", example = "JPEG")
        private String format;

        /** 文件大小，单位字节 */
        @Schema(description = "文件大小(字节)", example = "1048576")
        private long fileSize;

        /**
         * 默认构造函数
         */
        public ImageInfo() {}

        /**
         * 全参数构造函数
         * 
         * @param width 图像宽度
         * @param height 图像高度
         * @param format 图像格式
         * @param fileSize 文件大小
         */
        public ImageInfo(int width, int height, String format, long fileSize) {
            this.width = width;
            this.height = height;
            this.format = format;
            this.fileSize = fileSize;
        }

        /**
         * 获取图像宽度
         * @return 图像宽度(像素)
         */
        public int getWidth() { return width; }
        
        /**
         * 设置图像宽度
         * @param width 图像宽度(像素)
         */
        public void setWidth(int width) { this.width = width; }

        /**
         * 获取图像高度
         * @return 图像高度(像素)
         */
        public int getHeight() { return height; }
        
        /**
         * 设置图像高度
         * @param height 图像高度(像素)
         */
        public void setHeight(int height) { this.height = height; }

        /**
         * 获取图像格式
         * @return 图像格式
         */
        public String getFormat() { return format; }
        
        /**
         * 设置图像格式
         * @param format 图像格式
         */
        public void setFormat(String format) { this.format = format; }

        /**
         * 获取文件大小
         * @return 文件大小(字节)
         */
        public long getFileSize() { return fileSize; }
        
        /**
         * 设置文件大小
         * @param fileSize 文件大小(字节)
         */
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
    }
}
