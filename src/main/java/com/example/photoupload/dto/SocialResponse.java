package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 社交功能响应DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "社交功能响应")
public class SocialResponse {

    @Schema(description = "操作是否成功", example = "true")
    @JsonProperty("success")
    private boolean success;

    @Schema(description = "响应消息")
    @JsonProperty("message")
    private String message;

    @Schema(description = "错误代码")
    @JsonProperty("errorCode")
    private String errorCode;

    @Schema(description = "分享ID")
    @JsonProperty("shareId")
    private String shareId;

    @Schema(description = "分享链接")
    @JsonProperty("shareUrl")
    private String shareUrl;

    @Schema(description = "点赞数", example = "42")
    @JsonProperty("likeCount")
    private int likeCount;

    @Schema(description = "评论数", example = "15")
    @JsonProperty("commentCount")
    private int commentCount;

    @Schema(description = "分享数", example = "8")
    @JsonProperty("shareCount")
    private int shareCount;

    @Schema(description = "是否已点赞", example = "true")
    @JsonProperty("isLiked")
    private boolean isLiked;

    @Schema(description = "是否已关注", example = "false")
    @JsonProperty("isFollowing")
    private boolean isFollowing;

    @Schema(description = "创建时间")
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;

    @Schema(description = "额外数据")
    @JsonProperty("data")
    private Map<String, Object> data;

    @Schema(description = "用户统计信息")
    @JsonProperty("userStats")
    private UserStats userStats;

    @Schema(description = "评论列表")
    @JsonProperty("comments")
    private List<Comment> comments;

    @Schema(description = "动态列表")
    @JsonProperty("feedItems")
    private List<FeedItem> feedItems;

    @Schema(description = "热门图片列表")
    @JsonProperty("trendingImages")
    private List<TrendingImage> trendingImages;

    // 构造函数
    public SocialResponse() {
        this.createdAt = LocalDateTime.now();
    }

    // 静态工厂方法
    public static SocialResponse success(String message) {
        SocialResponse response = new SocialResponse();
        response.setSuccess(true);
        response.setMessage(message);
        return response;
    }

    public static SocialResponse error(String message) {
        SocialResponse response = new SocialResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }

    public static SocialResponse error(String message, String errorCode) {
        SocialResponse response = error(message);
        response.setErrorCode(errorCode);
        return response;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl;
    }

    public int getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(int likeCount) {
        this.likeCount = likeCount;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public int getShareCount() {
        return shareCount;
    }

    public void setShareCount(int shareCount) {
        this.shareCount = shareCount;
    }

    public boolean isLiked() {
        return isLiked;
    }

    public void setLiked(boolean liked) {
        isLiked = liked;
    }

    public boolean isFollowing() {
        return isFollowing;
    }

    public void setFollowing(boolean following) {
        isFollowing = following;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public UserStats getUserStats() {
        return userStats;
    }

    public void setUserStats(UserStats userStats) {
        this.userStats = userStats;
    }

    public List<Comment> getComments() {
        return comments;
    }

    public void setComments(List<Comment> comments) {
        this.comments = comments;
    }

    public List<FeedItem> getFeedItems() {
        return feedItems;
    }

    public void setFeedItems(List<FeedItem> feedItems) {
        this.feedItems = feedItems;
    }

    public List<TrendingImage> getTrendingImages() {
        return trendingImages;
    }

    public void setTrendingImages(List<TrendingImage> trendingImages) {
        this.trendingImages = trendingImages;
    }

    /**
     * 评论信息内部类
     */
    @Schema(description = "评论信息")
    public static class Comment {
        @Schema(description = "评论ID")
        private String commentId;

        @Schema(description = "用户ID")
        private String userId;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "用户头像")
        private String avatar;

        @Schema(description = "评论内容")
        private String content;

        @Schema(description = "点赞数")
        private int likeCount;

        @Schema(description = "是否已点赞")
        private boolean isLiked;

        @Schema(description = "回复数")
        private int replyCount;

        @Schema(description = "父评论ID")
        private String parentCommentId;

        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        @Schema(description = "回复列表")
        private List<Comment> replies;

        // Getters and Setters
        public String getCommentId() { return commentId; }
        public void setCommentId(String commentId) { this.commentId = commentId; }

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public int getLikeCount() { return likeCount; }
        public void setLikeCount(int likeCount) { this.likeCount = likeCount; }

        public boolean isLiked() { return isLiked; }
        public void setLiked(boolean liked) { isLiked = liked; }

        public int getReplyCount() { return replyCount; }
        public void setReplyCount(int replyCount) { this.replyCount = replyCount; }

        public String getParentCommentId() { return parentCommentId; }
        public void setParentCommentId(String parentCommentId) { this.parentCommentId = parentCommentId; }

        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

        public List<Comment> getReplies() { return replies; }
        public void setReplies(List<Comment> replies) { this.replies = replies; }
    }

    /**
     * 用户统计信息内部类
     */
    @Schema(description = "用户统计信息")
    public static class UserStats {
        @Schema(description = "关注数")
        private int followingCount;

        @Schema(description = "粉丝数")
        private int followersCount;

        @Schema(description = "发布图片数")
        private int imagesCount;

        @Schema(description = "获得点赞总数")
        private int totalLikes;

        @Schema(description = "获得评论总数")
        private int totalComments;

        @Schema(description = "获得分享总数")
        private int totalShares;

        @Schema(description = "活跃度分数")
        private double activityScore;

        @Schema(description = "影响力分数")
        private double influenceScore;

        @Schema(description = "最后活跃时间")
        private LocalDateTime lastActiveAt;

        // Getters and Setters
        public int getFollowingCount() { return followingCount; }
        public void setFollowingCount(int followingCount) { this.followingCount = followingCount; }

        public int getFollowersCount() { return followersCount; }
        public void setFollowersCount(int followersCount) { this.followersCount = followersCount; }

        public int getImagesCount() { return imagesCount; }
        public void setImagesCount(int imagesCount) { this.imagesCount = imagesCount; }

        public int getTotalLikes() { return totalLikes; }
        public void setTotalLikes(int totalLikes) { this.totalLikes = totalLikes; }

        public int getTotalComments() { return totalComments; }
        public void setTotalComments(int totalComments) { this.totalComments = totalComments; }

        public int getTotalShares() { return totalShares; }
        public void setTotalShares(int totalShares) { this.totalShares = totalShares; }

        public double getActivityScore() { return activityScore; }
        public void setActivityScore(double activityScore) { this.activityScore = activityScore; }

        public double getInfluenceScore() { return influenceScore; }
        public void setInfluenceScore(double influenceScore) { this.influenceScore = influenceScore; }

        public LocalDateTime getLastActiveAt() { return lastActiveAt; }
        public void setLastActiveAt(LocalDateTime lastActiveAt) { this.lastActiveAt = lastActiveAt; }
    }

    /**
     * 动态信息内部类
     */
    @Schema(description = "动态信息")
    public static class FeedItem {
        @Schema(description = "动态ID")
        private String feedId;

        @Schema(description = "用户ID")
        private String userId;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "用户头像")
        private String avatar;

        @Schema(description = "动态类型")
        private String type; // image_upload, like, comment, follow

        @Schema(description = "图片ID")
        private String imageId;

        @Schema(description = "图片URL")
        private String imageUrl;

        @Schema(description = "缩略图URL")
        private String thumbnailUrl;

        @Schema(description = "动态内容")
        private String content;

        @Schema(description = "点赞数")
        private int likeCount;

        @Schema(description = "评论数")
        private int commentCount;

        @Schema(description = "分享数")
        private int shareCount;

        @Schema(description = "是否已点赞")
        private boolean isLiked;

        @Schema(description = "标签列表")
        private List<String> tags;

        @Schema(description = "位置信息")
        private String location;

        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        // Getters and Setters
        public String getFeedId() { return feedId; }
        public void setFeedId(String feedId) { this.feedId = feedId; }

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }

        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        public String getImageId() { return imageId; }
        public void setImageId(String imageId) { this.imageId = imageId; }

        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }

        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public int getLikeCount() { return likeCount; }
        public void setLikeCount(int likeCount) { this.likeCount = likeCount; }

        public int getCommentCount() { return commentCount; }
        public void setCommentCount(int commentCount) { this.commentCount = commentCount; }

        public int getShareCount() { return shareCount; }
        public void setShareCount(int shareCount) { this.shareCount = shareCount; }

        public boolean isLiked() { return isLiked; }
        public void setLiked(boolean liked) { isLiked = liked; }

        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }

        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }

        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    }

    /**
     * 热门图片信息内部类
     */
    @Schema(description = "热门图片信息")
    public static class TrendingImage {
        @Schema(description = "图片ID")
        private String imageId;

        @Schema(description = "图片URL")
        private String imageUrl;

        @Schema(description = "缩略图URL")
        private String thumbnailUrl;

        @Schema(description = "图片标题")
        private String title;

        @Schema(description = "图片描述")
        private String description;

        @Schema(description = "上传用户ID")
        private String userId;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "用户头像")
        private String avatar;

        @Schema(description = "点赞数")
        private int likeCount;

        @Schema(description = "评论数")
        private int commentCount;

        @Schema(description = "分享数")
        private int shareCount;

        @Schema(description = "浏览数")
        private int viewCount;

        @Schema(description = "热度分数")
        private double trendingScore;

        @Schema(description = "排名")
        private int rank;

        @Schema(description = "标签列表")
        private List<String> tags;

        @Schema(description = "分类")
        private String category;

        @Schema(description = "是否已点赞")
        private boolean isLiked;

        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        @Schema(description = "上榜时间")
        private LocalDateTime trendingAt;

        // Getters and Setters
        public String getImageId() { return imageId; }
        public void setImageId(String imageId) { this.imageId = imageId; }

        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }

        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }

        public int getLikeCount() { return likeCount; }
        public void setLikeCount(int likeCount) { this.likeCount = likeCount; }

        public int getCommentCount() { return commentCount; }
        public void setCommentCount(int commentCount) { this.commentCount = commentCount; }

        public int getShareCount() { return shareCount; }
        public void setShareCount(int shareCount) { this.shareCount = shareCount; }

        public int getViewCount() { return viewCount; }
        public void setViewCount(int viewCount) { this.viewCount = viewCount; }

        public double getTrendingScore() { return trendingScore; }
        public void setTrendingScore(double trendingScore) { this.trendingScore = trendingScore; }

        public int getRank() { return rank; }
        public void setRank(int rank) { this.rank = rank; }

        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }

        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public boolean isLiked() { return isLiked; }
        public void setLiked(boolean liked) { isLiked = liked; }

        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

        public LocalDateTime getTrendingAt() { return trendingAt; }
        public void setTrendingAt(LocalDateTime trendingAt) { this.trendingAt = trendingAt; }
    }
}
