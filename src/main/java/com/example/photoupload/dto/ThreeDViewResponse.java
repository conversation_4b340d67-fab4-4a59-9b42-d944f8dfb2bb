package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 3D视图响应DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "3D视图响应")
public class ThreeDViewResponse {

    @Schema(description = "是否成功", example = "true")
    @JsonProperty("success")
    private boolean success;

    @Schema(description = "响应消息", example = "3D模型生成成功")
    @JsonProperty("message")
    private String message;

    @Schema(description = "模型ID", example = "model_123456")
    @JsonProperty("modelId")
    private String modelId;

    @Schema(description = "模型文件URL", example = "/api/3d/model/model_123456")
    @JsonProperty("modelUrl")
    private String modelUrl;

    @Schema(description = "纹理文件URL", example = "/api/3d/texture/model_123456")
    @JsonProperty("textureUrl")
    private String textureUrl;

    @Schema(description = "预览图URL", example = "/api/files/preview/model_123456_preview.jpg")
    @JsonProperty("previewUrl")
    private String previewUrl;

    @Schema(description = "模型类型", example = "cube")
    @JsonProperty("modelType")
    private String modelType;

    @Schema(description = "模型信息")
    @JsonProperty("modelInfo")
    private ModelInfo modelInfo;

    @Schema(description = "查看器配置")
    @JsonProperty("viewerConfig")
    private ViewerConfig viewerConfig;

    @Schema(description = "处理时间(毫秒)", example = "5000")
    @JsonProperty("processingTime")
    private Long processingTime;

    @Schema(description = "创建时间")
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    @Schema(description = "额外数据")
    @JsonProperty("extraData")
    private Map<String, Object> extraData;

    // 构造函数
    public ThreeDViewResponse() {
        this.createdAt = LocalDateTime.now();
    }

    // 便捷方法
    public static ThreeDViewResponse success(String modelId, String message) {
        ThreeDViewResponse response = new ThreeDViewResponse();
        response.setSuccess(true);
        response.setModelId(modelId);
        response.setMessage(message);
        return response;
    }

    public static ThreeDViewResponse error(String message) {
        ThreeDViewResponse response = new ThreeDViewResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getModelUrl() {
        return modelUrl;
    }

    public void setModelUrl(String modelUrl) {
        this.modelUrl = modelUrl;
    }

    public String getTextureUrl() {
        return textureUrl;
    }

    public void setTextureUrl(String textureUrl) {
        this.textureUrl = textureUrl;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public ModelInfo getModelInfo() {
        return modelInfo;
    }

    public void setModelInfo(ModelInfo modelInfo) {
        this.modelInfo = modelInfo;
    }

    public ViewerConfig getViewerConfig() {
        return viewerConfig;
    }

    public void setViewerConfig(ViewerConfig viewerConfig) {
        this.viewerConfig = viewerConfig;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Map<String, Object> getExtraData() {
        return extraData;
    }

    public void setExtraData(Map<String, Object> extraData) {
        this.extraData = extraData;
    }

    /**
     * 模型信息内部类
     */
    @Schema(description = "模型信息")
    public static class ModelInfo {
        @Schema(description = "顶点数量", example = "1024")
        private int vertexCount;

        @Schema(description = "面数量", example = "2048")
        private int faceCount;

        @Schema(description = "纹理分辨率", example = "1024x1024")
        private String textureResolution;

        @Schema(description = "文件大小(字节)", example = "1048576")
        private long fileSize;

        @Schema(description = "模型格式", example = "OBJ")
        private String format;

        @Schema(description = "材质列表")
        private List<String> materials;

        // 构造函数
        public ModelInfo() {}

        public ModelInfo(int vertexCount, int faceCount, String textureResolution, long fileSize, String format) {
            this.vertexCount = vertexCount;
            this.faceCount = faceCount;
            this.textureResolution = textureResolution;
            this.fileSize = fileSize;
            this.format = format;
        }

        // Getters and Setters
        public int getVertexCount() { return vertexCount; }
        public void setVertexCount(int vertexCount) { this.vertexCount = vertexCount; }

        public int getFaceCount() { return faceCount; }
        public void setFaceCount(int faceCount) { this.faceCount = faceCount; }

        public String getTextureResolution() { return textureResolution; }
        public void setTextureResolution(String textureResolution) { this.textureResolution = textureResolution; }

        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }

        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }

        public List<String> getMaterials() { return materials; }
        public void setMaterials(List<String> materials) { this.materials = materials; }
    }

    /**
     * 查看器配置内部类
     */
    @Schema(description = "查看器配置")
    public static class ViewerConfig {
        @Schema(description = "是否启用控制器", example = "true")
        private boolean enableControls = true;

        @Schema(description = "是否自动旋转", example = "true")
        private boolean autoRotate = true;

        @Schema(description = "旋转速度", example = "1.0")
        private float rotationSpeed = 1.0f;

        @Schema(description = "缩放范围")
        private ZoomRange zoomRange;

        @Schema(description = "背景设置")
        private BackgroundSettings background;

        // 构造函数
        public ViewerConfig() {
            this.zoomRange = new ZoomRange();
            this.background = new BackgroundSettings();
        }

        // Getters and Setters
        public boolean isEnableControls() { return enableControls; }
        public void setEnableControls(boolean enableControls) { this.enableControls = enableControls; }

        public boolean isAutoRotate() { return autoRotate; }
        public void setAutoRotate(boolean autoRotate) { this.autoRotate = autoRotate; }

        public float getRotationSpeed() { return rotationSpeed; }
        public void setRotationSpeed(float rotationSpeed) { this.rotationSpeed = rotationSpeed; }

        public ZoomRange getZoomRange() { return zoomRange; }
        public void setZoomRange(ZoomRange zoomRange) { this.zoomRange = zoomRange; }

        public BackgroundSettings getBackground() { return background; }
        public void setBackground(BackgroundSettings background) { this.background = background; }

        /**
         * 缩放范围内部类
         */
        public static class ZoomRange {
            private float min = 0.1f;
            private float max = 10.0f;

            public float getMin() { return min; }
            public void setMin(float min) { this.min = min; }

            public float getMax() { return max; }
            public void setMax(float max) { this.max = max; }
        }

        /**
         * 背景设置内部类
         */
        public static class BackgroundSettings {
            private String type = "color"; // color, gradient, skybox
            private String color = "#000000";
            private String gradientStart = "#000000";
            private String gradientEnd = "#333333";

            public String getType() { return type; }
            public void setType(String type) { this.type = type; }

            public String getColor() { return color; }
            public void setColor(String color) { this.color = color; }

            public String getGradientStart() { return gradientStart; }
            public void setGradientStart(String gradientStart) { this.gradientStart = gradientStart; }

            public String getGradientEnd() { return gradientEnd; }
            public void setGradientEnd(String gradientEnd) { this.gradientEnd = gradientEnd; }
        }
    }
}
