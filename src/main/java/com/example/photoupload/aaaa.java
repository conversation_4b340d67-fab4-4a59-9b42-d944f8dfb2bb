package com.example.photoupload;

/**
 * 示例类 - 用于演示
 * 
 * 这个类包含了一些基本的功能示例，用于展示如何组织代码。
 */
public class AAAA {
    
    /**
     * 主方法 - 程序入口
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("Hello, this is AAAA class!");
        
        // 示例方法调用
        String message = getGreeting("World");
        System.out.println(message);
    }
    
    /**
     * 获取问候语
     * 
     * @param name 名称
     * @return 问候语字符串
     */
    public static String getGreeting(String name) {
        return "Hello, " + name + "! Welcome to the Smart Album System!";
    }
    
    /**
     * 示例方法：计算两个数的和
     * 
     * @param a 第一个加数
     * @param b 第二个加数
     * @return 两数之和
     */
    public int add(int a, int b) {
        return a + b;
    }
    
    /**
     * 示例方法：检查数字是否为偶数
     * 
     * @param number 要检查的数字
     * @return 如果是偶数返回true，否则返回false
     */
    public boolean isEven(int number) {
        return number % 2 == 0;
    }
}
