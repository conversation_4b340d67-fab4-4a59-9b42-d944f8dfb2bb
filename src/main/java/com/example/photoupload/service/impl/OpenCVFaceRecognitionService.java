package com.example.photoupload.service.impl;

import com.example.photoupload.dto.FaceDetectionResult;
import com.example.photoupload.service.FaceRecognitionService;
import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.opencv.objdetect.CascadeClassifier;
import org.opencv.objdetect.Objdetect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于OpenCV的人脸识别服务实现
 */
@Service
public class OpenCVFaceRecognitionService implements FaceRecognitionService {

    @Value("${opencv.haarcascade.path:haarcascades/haarcascade_frontalface_default.xml}")
    private String cascadePath;

    private CascadeClassifier faceCascade;
    
    @PostConstruct
    public void init() {
        try {
            // 加载OpenCV本地库
            nu.pattern.OpenCV.loadLocally();
            
            // 加载人脸检测分类器
            ClassPathResource resource = new ClassPathResource(cascadePath);
            faceCascade = new CascadeClassifier(resource.getFile().getAbsolutePath());
            
            if (faceCascade.empty()) {
                throw new RuntimeException("无法加载人脸检测分类器: " + cascadePath);
            }
        } catch (IOException e) {
            throw new RuntimeException("初始化人脸检测服务失败", e);
        }
    }

    @Override
    public FaceDetectionResult detectFaces(InputStream imageStream, String fileName) {
        try {
            // 将输入流转换为OpenCV的Mat对象
            Mat image = Imgcodecs.imdecode(new MatOfByte(imageStream.readAllBytes()), Imgcodecs.IMREAD_COLOR);
            
            // 转换为灰度图
            Mat grayImage = new Mat();
            Imgproc.cvtColor(image, grayImage, Imgproc.COLOR_BGR2GRAY);
            
            // 检测人脸
            MatOfRect faceDetections = new MatOfRect();
            faceCascade.detectMultiScale(grayImage, faceDetections);
            
            // 处理检测到的人脸
            List<FaceDetectionResult.Face> faces = new ArrayList<>();
            for (Rect rect : faceDetections.toArray()) {
                // 识别人脸特征点
                Mat faceROI = new Mat(grayImage, rect);
                
                // 创建人脸结果对象
                FaceDetectionResult.Face face = new FaceDetectionResult.Face();
                face.setFaceId(UUID.randomUUID().toString());
                face.setX(rect.x);
                face.setY(rect.y);
                face.setWidth(rect.width);
                face.setHeight(rect.height);
                face.setConfidence(1.0); // 简单实现中固定置信度
                
                // 这里可以添加性别、年龄、情绪等分析
                face.setGender(detectGender(faceROI));
                face.setAge(estimateAge(faceROI));
                face.setEmotion(detectEmotion(faceROI));
                
                faces.add(face);
            }
            
            // 返回检测结果
            FaceDetectionResult result = new FaceDetectionResult();
            result.setImageId(fileName);
            result.setFaces(faces);
            result.setFaceCount(faces.size());
            result.setDominantEmotion(findDominantEmotion(faces));
            
            return result;
            
        } catch (Exception e) {
            throw new RuntimeException("人脸检测失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FaceDetectionResult.Face> recognizeAndGroupFaces(InputStream imageStream, String fileName) {
        // 简单实现：直接返回检测到的人脸，实际项目中应该使用更复杂的算法进行分组
        return detectFaces(imageStream, fileName).getFaces();
    }

    @Override
    public String matchKnownFace(InputStream imageStream, List<FaceDetectionResult.Face> knownFaces) {
        // 简单实现：返回匹配到的人脸ID，实际项目中应该使用特征向量匹配
        if (knownFaces == null || knownFaces.isEmpty()) {
            return "unknown";
        }
        return knownFaces.get(0).getFaceId();
    }

    @Override
    public float[] extractFaceFeatures(byte[] face) {
        // 简单实现：返回随机特征向量，实际项目中应该使用深度学习模型提取特征
        float[] features = new float[128];
        Random random = new Random();
        for (int i = 0; i < features.length; i++) {
            features[i] = random.nextFloat();
        }
        return features;
    }
    
    // 辅助方法：检测性别
    private String detectGender(Mat face) {
        // 简单实现：随机返回性别，实际项目中应该使用性别检测模型
        return new Random().nextBoolean() ? "male" : "female";
    }
    
    // 辅助方法：估计年龄
    private int estimateAge(Mat face) {
        // 简单实现：返回随机年龄，实际项目中应该使用年龄估计模型
        return 20 + new Random().nextInt(50);
    }
    
    // 辅助方法：检测情绪
    private String detectEmotion(Mat face) {
        // 简单实现：随机返回情绪，实际项目中应该使用情绪识别模型
        String[] emotions = {"happy", "sad", "neutral", "surprise", "angry"};
        return emotions[new Random().nextInt(emotions.length)];
    }
    
    // 辅助方法：找出主要情绪
    private String findDominantEmotion(List<FaceDetectionResult.Face> faces) {
        if (faces == null || faces.isEmpty()) {
            return "neutral";
        }
        
        // 统计每种情绪的出现次数
        Map<String, Long> emotionCounts = faces.stream()
                .collect(Collectors.groupingBy(FaceDetectionResult.Face::getEmotion, Collectors.counting()));
        
        // 返回出现次数最多的情绪
        return emotionCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("neutral");
    }
}
