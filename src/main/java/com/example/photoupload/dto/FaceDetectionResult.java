package com.example.photoupload.dto;

import java.util.List;
import java.util.Objects;

/**
 * 人脸检测结果
 */
public class FaceDetectionResult {
    private String imageId;
    private List<Face> faces;
    private int faceCount;
    private String dominantEmotion;

    public FaceDetectionResult() {
    }

    public FaceDetectionResult(String imageId, List<Face> faces, int faceCount, String dominantEmotion) {
        this.imageId = imageId;
        this.faces = faces;
        this.faceCount = faceCount;
        this.dominantEmotion = dominantEmotion;
    }

    // Getters and Setters
    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public List<Face> getFaces() {
        return faces;
    }

    public void setFaces(List<Face> faces) {
        this.faces = faces;
    }

    public int getFaceCount() {
        return faceCount;
    }

    public void setFaceCount(int faceCount) {
        this.faceCount = faceCount;
    }

    public String getDominantEmotion() {
        return dominantEmotion;
    }

    public void setDominantEmotion(String dominantEmotion) {
        this.dominantEmotion = dominantEmotion;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FaceDetectionResult that = (FaceDetectionResult) o;
        return faceCount == that.faceCount &&
                Objects.equals(imageId, that.imageId) &&
                Objects.equals(faces, that.faces) &&
                Objects.equals(dominantEmotion, that.dominantEmotion);
    }

    @Override
    public int hashCode() {
        return Objects.hash(imageId, faces, faceCount, dominantEmotion);
    }

    @Override
    public String toString() {
        return "FaceDetectionResult{" +
                "imageId='" + imageId + '\'' +
                ", faces=" + faces +
                ", faceCount=" + faceCount +
                ", dominantEmotion='" + dominantEmotion + '\'' +
                '}';
    }

    public static class Face {
        private String faceId;
        private int x;
        private int y;
        private int width;
        private int height;
        private double confidence;
        private String gender;
        private int age;
        private String emotion;
        private List<Landmark> landmarks;

        public Face() {
        }

        public Face(String faceId, int x, int y, int width, int height, double confidence, 
                   String gender, int age, String emotion, List<Landmark> landmarks) {
            this.faceId = faceId;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.confidence = confidence;
            this.gender = gender;
            this.age = age;
            this.emotion = emotion;
            this.landmarks = landmarks;
        }

        // Getters and Setters
        public String getFaceId() { return faceId; }
        public void setFaceId(String faceId) { this.faceId = faceId; }
        public int getX() { return x; }
        public void setX(int x) { this.x = x; }
        public int getY() { return y; }
        public void setY(int y) { this.y = y; }
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }
        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
        public double getConfidence() { return confidence; }
        public void setConfidence(double confidence) { this.confidence = confidence; }
        public String getGender() { return gender; }
        public void setGender(String gender) { this.gender = gender; }
        public int getAge() { return age; }
        public void setAge(int age) { this.age = age; }
        public String getEmotion() { return emotion; }
        public void setEmotion(String emotion) { this.emotion = emotion; }
        public List<Landmark> getLandmarks() { return landmarks; }
        public void setLandmarks(List<Landmark> landmarks) { this.landmarks = landmarks; }
    }
    
    public static class Landmark {
        private String type; // "eye", "nose", "mouth", etc.
        private int x;
        private int y;

        public Landmark() {
        }

        public Landmark(String type, int x, int y) {
            this.type = type;
            this.x = x;
            this.y = y;
        }

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public int getX() { return x; }
        public void setX(int x) { this.x = x; }
        public int getY() { return y; }
        public void setY(int y) { this.y = y; }
    }
}
