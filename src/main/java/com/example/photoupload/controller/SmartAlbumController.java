package com.example.photoupload.controller;

import com.example.photoupload.dto.SmartAlbumRequest;
import com.example.photoupload.dto.SmartAlbumResponse;
import com.example.photoupload.service.SmartAlbumService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI智能相册控制器
 * 提供基于AI分析的智能相册功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/smart-albums")
@Tag(name = "AI智能相册", description = "基于AI分析的智能相册管理API")
public class SmartAlbumController {

    private static final Logger logger = LoggerFactory.getLogger(SmartAlbumController.class);

    @Autowired
    private SmartAlbumService smartAlbumService;

    /**
     * 创建智能相册
     */
    @PostMapping("/create")
    @Operation(summary = "创建智能相册", description = "基于AI分析结果自动创建智能相册")
    public ResponseEntity<SmartAlbumResponse> createSmartAlbum(@RequestBody SmartAlbumRequest request) {
        try {
            logger.info("创建智能相册请求: type={}, criteria={}", request.getAlbumType(), request.getCriteria());
            
            SmartAlbumResponse response = smartAlbumService.createSmartAlbum(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("创建智能相册失败", e);
            SmartAlbumResponse errorResponse = SmartAlbumResponse.error("创建智能相册失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取所有智能相册
     */
    @GetMapping("/list")
    @Operation(summary = "获取智能相册列表", description = "获取所有智能相册")
    public ResponseEntity<List<SmartAlbumResponse>> getSmartAlbums() {
        try {
            logger.info("获取智能相册列表");
            
            List<SmartAlbumResponse> albums = smartAlbumService.getAllSmartAlbums();
            
            return ResponseEntity.ok(albums);
            
        } catch (Exception e) {
            logger.error("获取智能相册列表失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取智能相册详情
     */
    @GetMapping("/{albumId}")
    @Operation(summary = "获取智能相册详情", description = "获取指定智能相册的详细信息")
    public ResponseEntity<SmartAlbumResponse> getSmartAlbum(@PathVariable String albumId) {
        try {
            logger.info("获取智能相册详情: albumId={}", albumId);
            
            SmartAlbumResponse album = smartAlbumService.getSmartAlbum(albumId);
            
            if (album != null) {
                return ResponseEntity.ok(album);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            logger.error("获取智能相册详情失败: albumId={}", albumId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新智能相册
     */
    @PutMapping("/{albumId}")
    @Operation(summary = "更新智能相册", description = "更新智能相册的设置和条件")
    public ResponseEntity<SmartAlbumResponse> updateSmartAlbum(
            @PathVariable String albumId, 
            @RequestBody SmartAlbumRequest request) {
        try {
            logger.info("更新智能相册: albumId={}", albumId);
            
            SmartAlbumResponse response = smartAlbumService.updateSmartAlbum(albumId, request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("更新智能相册失败: albumId={}", albumId, e);
            SmartAlbumResponse errorResponse = SmartAlbumResponse.error("更新智能相册失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 删除智能相册
     */
    @DeleteMapping("/{albumId}")
    @Operation(summary = "删除智能相册", description = "删除指定的智能相册")
    public ResponseEntity<SmartAlbumResponse> deleteSmartAlbum(@PathVariable String albumId) {
        try {
            logger.info("删除智能相册: albumId={}", albumId);
            
            boolean deleted = smartAlbumService.deleteSmartAlbum(albumId);
            
            SmartAlbumResponse response = new SmartAlbumResponse();
            response.setSuccess(deleted);
            response.setMessage(deleted ? "删除成功" : "删除失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除智能相册失败: albumId={}", albumId, e);
            SmartAlbumResponse errorResponse = SmartAlbumResponse.error("删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 刷新智能相册
     */
    @PostMapping("/{albumId}/refresh")
    @Operation(summary = "刷新智能相册", description = "重新分析并更新智能相册内容")
    public ResponseEntity<SmartAlbumResponse> refreshSmartAlbum(@PathVariable String albumId) {
        try {
            logger.info("刷新智能相册: albumId={}", albumId);
            
            SmartAlbumResponse response = smartAlbumService.refreshSmartAlbum(albumId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("刷新智能相册失败: albumId={}", albumId, e);
            SmartAlbumResponse errorResponse = SmartAlbumResponse.error("刷新失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取推荐相册
     */
    @GetMapping("/recommendations")
    @Operation(summary = "获取推荐相册", description = "基于用户图片获取智能相册推荐")
    public ResponseEntity<List<SmartAlbumResponse>> getRecommendations() {
        try {
            logger.info("获取智能相册推荐");
            
            List<SmartAlbumResponse> recommendations = smartAlbumService.getAlbumRecommendations();
            
            return ResponseEntity.ok(recommendations);
            
        } catch (Exception e) {
            logger.error("获取智能相册推荐失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 搜索智能相册
     */
    @GetMapping("/search")
    @Operation(summary = "搜索智能相册", description = "根据关键词搜索智能相册")
    public ResponseEntity<List<SmartAlbumResponse>> searchSmartAlbums(@RequestParam String query) {
        try {
            logger.info("搜索智能相册: query={}", query);
            
            List<SmartAlbumResponse> results = smartAlbumService.searchSmartAlbums(query);
            
            return ResponseEntity.ok(results);
            
        } catch (Exception e) {
            logger.error("搜索智能相册失败: query={}", query, e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取相册统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取相册统计", description = "获取智能相册的统计信息")
    public ResponseEntity<SmartAlbumResponse> getAlbumStatistics() {
        try {
            logger.info("获取智能相册统计信息");
            
            SmartAlbumResponse statistics = smartAlbumService.getAlbumStatistics();
            
            return ResponseEntity.ok(statistics);
            
        } catch (Exception e) {
            logger.error("获取智能相册统计信息失败", e);
            SmartAlbumResponse errorResponse = SmartAlbumResponse.error("获取统计信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 批量操作智能相册
     */
    @PostMapping("/batch")
    @Operation(summary = "批量操作智能相册", description = "批量创建、更新或删除智能相册")
    public ResponseEntity<List<SmartAlbumResponse>> batchOperations(@RequestBody List<SmartAlbumRequest> requests) {
        try {
            logger.info("批量操作智能相册: count={}", requests.size());
            
            List<SmartAlbumResponse> responses = smartAlbumService.batchOperations(requests);
            
            return ResponseEntity.ok(responses);
            
        } catch (Exception e) {
            logger.error("批量操作智能相册失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
}
