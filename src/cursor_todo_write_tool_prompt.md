{
      type: "function", // 定义函数类型的工具
      function: {
        name: "todo_write", // 工具名称：todo_write，用于创建和管理待办事项列表
        description: `Use this tool to create and manage a structured task list for your current coding session. This helps track progress, organize complex tasks, and demonstrate thoroughness.
        // 工具描述：使用此工具为当前编码会话创建和管理结构化任务列表。这有助于跟踪进度、组织复杂任务并展示全面性。

### When to Use This Tool
// ### 何时使用此工具

Use proactively for:
// 积极使用于以下情况：
1. Complex multi-step tasks (3+ distinct steps)
   // 1. 复杂的多步骤任务（3个或更多不同步骤）
2. Non-trivial tasks requiring careful planning
   // 2. 需要仔细规划的非平凡任务
3. User explicitly requests todo list
   // 3. 用户明确请求待办事项列表
4. User provides multiple tasks (numbered/comma-separated)
   // 4. 用户提供多个任务（编号或逗号分隔）
5. After receiving new instructions - capture requirements as todos (use merge=false to add new ones)
   // 5. 收到新指令后 - 将需求作为待办事项捕获（使用 merge=false 添加新任务）
6. After completing tasks - mark complete with merge=true and add follow-ups
   // 6. 完成任务后 - 使用 merge=true 标记完成并添加后续任务
7. When starting new tasks - mark as in_progress (ideally only one at a time)
   // 7. 开始新任务时 - 标记为进行中（理想情况下一次只有一个）

### When NOT to Use
// ### 何时不使用

Skip for:
// 跳过以下情况：
1. Single, straightforward tasks
   // 1. 单一、直接的任务
2. Trivial tasks with no organizational benefit
   // 2. 没有组织效益的琐碎任务
3. Tasks completable in < 3 trivial steps
   // 3. 可以在少于3个简单步骤内完成的任务
4. Purely conversational/informational requests
   // 4. 纯粹的对话/信息请求
5. Don't add a task to test the change unless asked, or you'll overfocus on testing
   // 5. 除非被要求，否则不要添加测试更改的任务，否则你会过度关注测试

### Examples
// ### 示例

<example>
  User: Add dark mode toggle to settings
  // 用户：在设置中添加暗模式切换
  Assistant: *Creates todo list:*
  // 助手：*创建待办事项列表：*
  1. Add state management - no dependencies
     // 1. 添加状态管理 - 无依赖
  2. Implement styles - depends on task 1
     // 2. 实现样式 - 依赖任务1
  3. Create toggle component - depends on tasks 1, 2
     // 3. 创建切换组件 - 依赖任务1、2
  4. Update components - depends on tasks 1, 2
     // 4. 更新组件 - 依赖任务1、2
<reasoning>
  Multi-step feature with dependencies; user requested tests/build afterward.
  // 推理：具有依赖关系的多步骤功能；用户之后请求测试/构建。
</reasoning>
</example>

<example>
  User: Rename getCwd to getCurrentWorkingDirectory across my project
  // 用户：在我的项目中将 getCwd 重命名为 getCurrentWorkingDirectory
  Assistant: *Searches codebase, finds 15 instances across 8 files*
  // 助手：*搜索代码库，在8个文件中找到15个实例*
  *Creates todo list with specific items for each file that needs updating*
  // *为每个需要更新的文件创建具体项目的待办事项列表*

<reasoning>
  Complex refactoring requiring systematic tracking across multiple files.
  // 推理：复杂的重构需要跨多个文件进行系统性跟踪。
</reasoning>
</example>

<example>
  User: Implement user registration, product catalog, shopping cart, checkout flow.
  // 用户：实现用户注册、产品目录、购物车、结账流程。
  Assistant: *Creates todo list breaking down each feature into specific tasks*
  // 助手：*创建待办事项列表，将每个功能分解为具体任务*

<reasoning>
  Multiple complex features provided as list requiring organized task management.
  // 推理：作为列表提供的多个复杂功能需要有组织的任务管理。
</reasoning>
</example>

<example>
  User: Optimize my React app - it's rendering slowly.
  // 用户：优化我的React应用 - 渲染很慢。
  Assistant: *Analyzes codebase, identifies issues*
  // 助手：*分析代码库，识别问题*
  *Creates todo list: 1) Memoization, 2) Virtualization, 3) Image optimization, 4) Fix state loops, 5) Code splitting*
  // *创建待办事项列表：1）记忆化，2）虚拟化，3）图像优化，4）修复状态循环，5）代码分割*

<reasoning>
  Performance optimization requires multiple steps across different components.
  // 推理：性能优化需要跨不同组件的多个步骤。
</reasoning>
</example>

### Examples of When NOT to Use the Todo List
// ### 不使用待办事项列表的示例

<example>
  User: How do I print 'Hello World' in Python?
  // 用户：如何在Python中打印'Hello World'？
  Assistant: \`\`\`python
print("Hello World")
\`\`\`

<reasoning>
  Single trivial task completed in one step.
  // 推理：一步完成的单一琐碎任务。
</reasoning>
</example>

<example>
  User: What does git status do?
  // 用户：git status 做什么？
  Assistant: Shows current state of working directory and staging area...
  // 助手：显示工作目录和暂存区的当前状态...

<reasoning>
  Informational request with no coding task to complete.
  // 推理：信息请求，没有需要完成的编码任务。
</reasoning>
</example>

<example>
  User: Add comment to calculateTotal function.
  // 用户：为 calculateTotal 函数添加注释。
  Assistant: *Uses edit tool to add comment*
  // 助手：*使用编辑工具添加注释*

<reasoning>
  Single straightforward task in one location.
  // 推理：在一个位置的单一直接任务。
</reasoning>
</example>

<example>
  User: Run npm install for me.
  // 用户：为我运行 npm install。
  Assistant: *Executes npm install* Command completed successfully...
  // 助手：*执行 npm install* 命令成功完成...

<reasoning>
  Single command execution with immediate results.
  // 推理：单一命令执行，立即得到结果。
</reasoning>
</example>

### Task States and Management
// ### 任务状态和管理

1. **Task States:**
   // **任务状态：**
  - pending: Not yet started
    // - 待定：尚未开始
  - in_progress: Currently working on
    // - 进行中：当前正在处理
  - completed: Finished successfully
    // - 已完成：成功完成
  - cancelled: No longer needed
    // - 已取消：不再需要

2. **Task Management:**
   // **任务管理：**
  - Update status in real-time
    // - 实时更新状态
  - Mark complete IMMEDIATELY after finishing
    // - 完成后立即标记为完成
  - Only ONE task in_progress at a time
    // - 一次只有一个任务处于进行中状态
  - Complete current tasks before starting new ones
    // - 在开始新任务之前完成当前任务

3. **Task Breakdown:**
   // **任务分解：**
  - Create specific, actionable items
    // - 创建具体的、可执行的项目
  - Break complex tasks into manageable steps
    // - 将复杂任务分解为可管理的步骤
  - Use clear, descriptive names
    // - 使用清晰、描述性的名称

4. **Task Dependencies:**
   // **任务依赖：**
  - Use dependencies field for natural prerequisites
    // - 使用依赖字段表示自然的先决条件
  - Avoid circular dependencies
    // - 避免循环依赖
  - Independent tasks can run in parallel
    // - 独立任务可以并行运行

When in doubt, use this tool. Proactive task management demonstrates attentiveness and ensures complete requirements.`,
// 如有疑问，请使用此工具。主动的任务管理展示了专注性并确保完整的需求。`,
        parameters: {
          type: "object", // 参数类型：对象
          properties: {
            merge: {
              type: "boolean", // 类型：布尔值
              description: "Whether to merge the todos with the existing todos. If true, the todos will be merged into the existing todos based on the id field. You can leave unchanged properties undefined. If false, the new todos will replace the existing todos.",
              // 描述：是否将待办事项与现有待办事项合并。如果为true，待办事项将基于id字段合并到现有待办事项中。你可以将未更改的属性保留为未定义。如果为false，新的待办事项将替换现有的待办事项。
            },
            todos: {
              type: "array", // 类型：数组
              description: "Array of TODO items to write to the workspace",
              // 描述：要写入工作区的待办事项数组
              items: {
                type: "object", // 数组项类型：对象
                properties: {
                  content: {
                    type: "string", // 类型：字符串
                    description: "The description/content of the TODO item",
                    // 描述：待办事项的描述/内容
                  },
                  status: {
                    type: "string", // 类型：字符串
                    enum: [
                      "pending",    // 待定
                      "in_progress", // 进行中
                      "completed",   // 已完成
                      "cancelled",   // 已取消
                    ],
                    description: "The current status of the TODO item",
                    // 描述：待办事项的当前状态
                  },
                  id: {
                    type: "string", // 类型：字符串
                    description: "Unique identifier for the TODO item",
                    // 描述：待办事项的唯一标识符
                  },
                  dependencies: {
                    type: "array", // 类型：数组
                    items: {
                      type: "string", // 数组项类型：字符串
                    },
                    description: "List of other task IDs that are prerequisites for this task, i.e. we cannot complete this task until these tasks are done",
                    // 描述：此任务的先决条件的其他任务ID列表，即在这些任务完成之前我们无法完成此任务
                  },
                },
                required: [
                  "content",      // 必需：内容
                  "status",       // 必需：状态
                  "id",          // 必需：ID
                  "dependencies", // 必需：依赖
                ],
              },
              minItems: 2, // 最少项目数：2
            },
          },
          required: [
            "merge", // 必需：合并标志
            "todos", // 必需：待办事项数组
          ],
        },
      },
    }