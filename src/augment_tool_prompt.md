# Augment Agent Tools Documentation
# Augment 智能代理工具文档

This document provides detailed information about all the tools available to Augment Agent.
本文档提供了 Augment 智能代理所有可用工具的详细信息。

## Process Management Tools
## 进程管理工具

### 1. launch-process
### 1. 启动进程
**Description**: Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`).
**描述**：使用 shell 命令启动新进程。进程可以是等待模式（`wait=true`）或非等待模式（`wait=false`）。

**Parameters**:
**参数**：
- `command` (required): The shell command to execute
- `command`（必需）：要执行的 shell 命令
- `wait` (required): Whether to wait for the command to complete
- `wait`（必需）：是否等待命令完成
- `max_wait_seconds` (required): Number of seconds to wait for the command to complete
- `max_wait_seconds`（必需）：等待命令完成的秒数
- `cwd` (required): Absolute path to the working directory for the command
- `cwd`（必需）：命令工作目录的绝对路径

**Notes**:
**注意事项**：
- Use `wait=true` for short commands or when you can't proceed until completion
- 对于短命令或需要等待完成才能继续的情况，使用 `wait=true`
- Use `wait=false` for background processes like servers or long-running tasks
- 对于后台进程（如服务器）或长时间运行的任务，使用 `wait=false`
- OS is Linux with bash shell
- 操作系统是带有 bash shell 的 Linux
- Can interact with local version control system
- 可以与本地版本控制系统交互

### 2. kill-process
### 2. 终止进程
**Description**: Kill a process by its terminal ID.
**描述**：通过终端 ID 终止进程。

**Parameters**:
**参数**：
- `terminal_id` (required): Terminal ID to kill
- `terminal_id`（必需）：要终止的终端 ID

### 3. read-process
### 3. 读取进程
**Description**: Read output from a terminal.
**描述**：从终端读取输出。

**Parameters**:
**参数**：
- `terminal_id` (required): Terminal ID to read from
- `terminal_id`（必需）：要读取的终端 ID
- `wait` (required): Whether to wait for the command to complete
- `wait`（必需）：是否等待命令完成
- `max_wait_seconds` (required): Number of seconds to wait for completion
- `max_wait_seconds`（必需）：等待完成的秒数

### 4. write-process
### 4. 写入进程
**Description**: Write input to a terminal.
**描述**：向终端写入输入。

**Parameters**:
**参数**：
- `terminal_id` (required): Terminal ID to write to
- `terminal_id`（必需）：要写入的终端 ID
- `input_text` (required): Text to write to the process's stdin
- `input_text`（必需）：写入进程标准输入的文本

### 5. list-processes
### 5. 列出进程
**Description**: List all known terminals created with the launch-process tool and their states.
**描述**：列出所有使用 launch-process 工具创建的已知终端及其状态。

**Parameters**: None
**参数**：无

## Web and Search Tools
## 网络和搜索工具

### 6. web-search
### 6. 网络搜索
**Description**: Search the web for information using Google's Custom Search API. Returns results in markdown format.
**描述**：使用 Google 自定义搜索 API 在网络上搜索信息。以 markdown 格式返回结果。

**Parameters**:
**参数**：
- `query` (required): The search query to send
- `query`（必需）：要发送的搜索查询
- `num_results` (optional): Number of results to return (1-10, default: 5)
- `num_results`（可选）：返回结果数量（1-10，默认：5）

### 7. web-fetch
### 7. 网页获取
**Description**: Fetches data from a webpage and converts it into Markdown.
**描述**：从网页获取数据并将其转换为 Markdown 格式。

**Parameters**:
**参数**：
- `url` (required): The URL to fetch
- `url`（必需）：要获取的 URL

**Notes**:
**注意事项**：
- Returns content in Markdown format
- 以 Markdown 格式返回内容
- If return is not valid Markdown, the tool cannot parse the page
- 如果返回的不是有效的 Markdown，工具无法解析该页面

## GitHub Integration
## GitHub 集成

### 8. github-api
### 8. GitHub API
**Description**: Make GitHub API calls with YAML formatted responses. All issues endpoints return both issues and PRs.
**描述**：进行 GitHub API 调用并返回 YAML 格式的响应。所有 issues 端点都会返回 issues 和 PR。

**Current Repository Information**:
**当前仓库信息**：
- Repository Root: /mnt/persist/workspace
- 仓库根目录：/mnt/persist/workspace
- Remote URL: https://github.com/zhouchengwu/spring-petclinic
- 远程 URL：https://github.com/zhouchengwu/spring-petclinic
- Current Branch: main
- 当前分支：main
- User Login: zhouchengwu
- 用户登录名：zhouchengwu
- User Email: <EMAIL>
- 用户邮箱：<EMAIL>

**Parameters**:
**参数**：
- `path` (required): GitHub API path (e.g., `/repos/{owner}/{repo}/issues`)
- `path`（必需）：GitHub API 路径（例如：`/repos/{owner}/{repo}/issues`）
- `method` (optional): HTTP method (GET, POST, PATCH, PUT - default: GET)
- `method`（可选）：HTTP 方法（GET、POST、PATCH、PUT - 默认：GET）
- `data` (optional): Data to send as query params (GET) or JSON body (POST)
- `data`（可选）：作为查询参数（GET）或 JSON 主体（POST）发送的数据
- `details` (optional): Include all fields if true, essential fields if false (default)
- `details`（可选）：如果为 true 则包含所有字段，如果为 false 则包含基本字段（默认）
- `summary` (optional): Human-readable summary of the API call
- `summary`（可选）：API 调用的人类可读摘要

**Common API Paths**:
**常用 API 路径**：
- `/repos/{owner}/{repo}/issues` - List issues and PRs
- `/repos/{owner}/{repo}/issues` - 列出 issues 和 PR
- `/repos/{owner}/{repo}/pulls` - List pull requests
- `/repos/{owner}/{repo}/pulls` - 列出拉取请求
- `/search/issues` - Search issues/PRs with specific criteria
- `/search/issues` - 使用特定条件搜索 issues/PR
- `/search/code` - Find code with specific content
- `/search/code` - 查找包含特定内容的代码
- `/search/commits` - Find commits with specific criteria
- `/search/commits` - 使用特定条件查找提交
- `/repos/{owner}/{repo}/commits/{sha}/check-runs` - Check CI status
- `/repos/{owner}/{repo}/commits/{sha}/check-runs` - 检查 CI 状态

**Important Notes**:
**重要注意事项**：
- All queries limited to current repository unless explicitly requested otherwise
- 所有查询都限制在当前仓库，除非明确要求其他方式
- Always create and push new branch before creating PR
- 在创建 PR 之前始终创建并推送新分支
- Verify all requirements fulfilled before PR creation
- 在创建 PR 之前验证所有要求都已满足
- Check CI status when pushing commits to PR
- 向 PR 推送提交时检查 CI 状态

## Code Management Tools
## 代码管理工具

### 9. codebase-retrieval
### 9. 代码库检索
**Description**: Augment's context engine - the world's best codebase context engine.
**描述**：Augment 的上下文引擎 - 世界上最好的代码库上下文引擎。

**Parameters**:
**参数**：
- `information_request` (required): Natural language description of code you're looking for
- `information_request`（必需）：您要查找的代码的自然语言描述

**Capabilities**:
**功能**：
- Uses proprietary retrieval/embedding model suite
- 使用专有的检索/嵌入模型套件
- Maintains real-time index of codebase
- 维护代码库的实时索引
- Retrieves across different programming languages
- 跨不同编程语言检索
- Reflects current state of codebase on disk
- 反映磁盘上代码库的当前状态
- No information on version control or code history
- 不包含版本控制或代码历史信息

### 10. save-file
### 10. 保存文件
**Description**: Save a new file with content. Cannot modify existing files.
**描述**：保存带有内容的新文件。不能修改现有文件。

**Parameters**:
**参数**：
- `instructions_reminder` (required): Must be exactly: "LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."
- `instructions_reminder`（必需）：必须完全是："LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."
- `path` (required): The path of the file to save
- `path`（必需）：要保存的文件路径
- `file_content` (required): The content of the file
- `file_content`（必需）：文件的内容
- `add_last_line_newline` (optional): Whether to add newline at end (default: true)
- `add_last_line_newline`（可选）：是否在末尾添加换行符（默认：true）

### 11. str-replace-editor
### 11. 字符串替换编辑器
**Description**: Tool for editing existing files. This is the ONLY tool for editing files.
**描述**：用于编辑现有文件的工具。这是编辑文件的唯一工具。

**Parameters**:
**参数**：
- `command` (required): Either 'str_replace' or 'insert'
- `command`（必需）：'str_replace' 或 'insert'
- `path` (required): File path relative to workspace root
- `path`（必需）：相对于工作区根目录的文件路径
- `instruction_reminder` (required): Must be exactly: "ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH."
- `instruction_reminder`（必需）：必须完全是："ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH."

**For str_replace command**:
**对于 str_replace 命令**：
- `old_str_1` (required): String to replace (must match exactly)
- `old_str_1`（必需）：要替换的字符串（必须完全匹配）
- `new_str_1` (required): Replacement string (can be empty to delete)
- `new_str_1`（必需）：替换字符串（可以为空以删除）
- `old_str_start_line_number_1` (required): Start line number (1-based, inclusive)
- `old_str_start_line_number_1`（必需）：起始行号（从1开始，包含）
- `old_str_end_line_number_1` (required): End line number (1-based, inclusive)
- `old_str_end_line_number_1`（必需）：结束行号（从1开始，包含）

**For insert command**:
**对于 insert 命令**：
- `insert_line_1` (required): Line number after which to insert (1-based, use 0 for beginning)
- `insert_line_1`（必需）：在其后插入的行号（从1开始，使用0表示开头）
- `new_str_1` (required): String to insert
- `new_str_1`（必需）：要插入的字符串

**Important Notes**:
**重要注意事项**：
- Can make multiple replacements/insertions in one call
- 可以在一次调用中进行多次替换/插入
- Line numbers are 1-based and inclusive
- 行号从1开始且包含边界
- Do NOT use sed or command line tools for editing
- 不要使用 sed 或命令行工具进行编辑
- Do NOT remove and recreate files
- 不要删除并重新创建文件
- Fit as many edits as possible in one call
- 在一次调用中尽可能多地进行编辑

### 12. remove-files
### 12. 删除文件
**Description**: Remove files safely. ONLY use this tool to delete files.
**描述**：安全删除文件。只能使用此工具删除文件。

**Parameters**:
**参数**：
- `file_paths` (required): Array of file paths to remove
- `file_paths`（必需）：要删除的文件路径数组

**Notes**:
**注意事项**：
- This is the only safe tool to delete files
- 这是删除文件的唯一安全工具
- User can undo the change
- 用户可以撤销更改
- Do NOT use shell or launch-process to remove files
- 不要使用 shell 或 launch-process 来删除文件

### 13. view
### 13. 查看
**Description**: View files and directories, search within files with regex.
**描述**：查看文件和目录，使用正则表达式在文件内搜索。

**Parameters**:
**参数**：
- `path` (required): File or directory path relative to workspace root
- `path`（必需）：相对于工作区根目录的文件或目录路径
- `type` (required): Either 'file' or 'directory'
- `type`（必需）：'file' 或 'directory'
- `view_range` (optional): Array of line numbers [start, end] for files (1-based, inclusive)
- `view_range`（可选）：文件的行号数组 [start, end]（从1开始，包含边界）
- `search_query_regex` (optional): Regex pattern to search for in files
- `search_query_regex`（可选）：在文件中搜索的正则表达式模式
- `case_sensitive` (optional): Case sensitivity for regex (default: false)
- `case_sensitive`（可选）：正则表达式的大小写敏感性（默认：false）
- `context_lines_before` (optional): Lines before regex match (default: 5)
- `context_lines_before`（可选）：正则匹配前的行数（默认：5）
- `context_lines_after` (optional): Lines after regex match (default: 5)
- `context_lines_after`（可选）：正则匹配后的行数（默认：5）

**File viewing**:
**文件查看**：
- Displays result of `cat -n` for files
- 显示文件的 `cat -n` 结果
- Lists files/subdirectories up to 2 levels for directories
- 列出目录的文件/子目录，最多2级
- Long output is truncated with `<response clipped>`
- 长输出会被截断并显示 `<response clipped>`

**Regex search**:
**正则搜索**：
- Only shows matching lines and context
- 只显示匹配的行和上下文
- Non-matching sections replaced with `...`
- 不匹配的部分用 `...` 替换
- Supports core regex syntax common to JavaScript and Rust
- 支持 JavaScript 和 Rust 共同的核心正则语法
- Avoid shorthand classes like `\d`, `\s`, `\w`
- 避免使用简写类如 `\d`、`\s`、`\w`

## Visualization Tools
## 可视化工具

### 14. render-mermaid
### 14. 渲染 Mermaid
**Description**: Render a Mermaid diagram from provided definition.
**描述**：从提供的定义渲染 Mermaid 图表。

**Parameters**:
**参数**：
- `diagram_definition` (required): The Mermaid diagram definition code
- `diagram_definition`（必需）：Mermaid 图表定义代码
- `title` (optional): Title for the diagram (default: "Mermaid Diagram")
- `title`（可选）：图表标题（默认："Mermaid Diagram"）

**Features**:
**功能**：
- Interactive diagram with pan/zoom controls
- 具有平移/缩放控件的交互式图表
- Copy functionality
- 复制功能
- Supports all standard Mermaid diagram types
- 支持所有标准 Mermaid 图表类型

## Best Practices
## 最佳实践

1. **Information Gathering**: Always use codebase-retrieval before making edits
1. **信息收集**：在进行编辑之前始终使用 codebase-retrieval
2. **Planning**: Create detailed plans before taking action
2. **规划**：在采取行动之前制定详细计划
3. **Package Management**: Use package managers instead of editing config files manually
3. **包管理**：使用包管理器而不是手动编辑配置文件
4. **Testing**: Suggest writing/updating tests after code changes
4. **测试**：在代码更改后建议编写/更新测试
5. **Conservative Approach**: Ask permission for potentially damaging actions
5. **保守方法**：对于可能造成损害的操作请求权限
6. **Code Display**: Wrap code in `<augment_code_snippet>` XML tags with path and mode attributes
6. **代码显示**：将代码包装在带有路径和模式属性的 `<augment_code_snippet>` XML 标签中
7. **Error Recovery**: Ask for help if going in circles or down rabbit holes
7. **错误恢复**：如果陷入循环或进入死胡同，请寻求帮助
