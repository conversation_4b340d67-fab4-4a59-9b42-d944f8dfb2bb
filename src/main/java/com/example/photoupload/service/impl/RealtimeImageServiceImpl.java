package com.example.photoupload.service.impl;

import com.example.photoupload.dto.RealtimeProcessRequest;
import com.example.photoupload.dto.RealtimeProcessResponse;
import com.example.photoupload.service.RealtimeImageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.RescaleOp;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 实时图像处理服务实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class RealtimeImageServiceImpl implements RealtimeImageService {

    private static final Logger logger = LoggerFactory.getLogger(RealtimeImageServiceImpl.class);

    // 存储会话处理状态
    private final ConcurrentHashMap<String, RealtimeProcessResponse> sessionStatus = new ConcurrentHashMap<>();
    
    // 存储会话取消标志
    private final ConcurrentHashMap<String, AtomicBoolean> sessionCancelFlags = new ConcurrentHashMap<>();

    @Override
    @Async
    public CompletableFuture<RealtimeProcessResponse> previewFilter(RealtimeProcessRequest request, String sessionId) {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                // 更新处理状态
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在应用滤镜...", 10);
                
                // 检查是否被取消
                if (isCancelled(sessionId)) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "处理已取消", "用户取消了处理");
                }
                
                // 解码图像数据
                BufferedImage image = decodeBase64Image(request.getImageData());
                if (image == null) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "图像解码失败", "无效的图像数据");
                }
                
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在处理图像...", 30);
                
                // 应用滤镜
                BufferedImage filteredImage = applyFilter(image, request.getFilterType());
                
                if (isCancelled(sessionId)) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "处理已取消", "用户取消了处理");
                }
                
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在生成预览...", 70);
                
                // 生成预览图像
                String previewData = encodeImageToBase64(filteredImage, "JPEG");
                
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "处理完成", 100);
                
                // 创建成功响应
                RealtimeProcessResponse response = RealtimeProcessResponse.success(sessionId, request.getRequestId(), "滤镜预览生成成功");
                response.setPreviewImageData(previewData);
                response.setProcessingTime(System.currentTimeMillis() - startTime);
                response.setImageInfo(new RealtimeProcessResponse.ImageInfo(
                    filteredImage.getWidth(), 
                    filteredImage.getHeight(), 
                    "JPEG", 
                    previewData.length()
                ));
                
                // 更新最终状态
                sessionStatus.put(sessionId, response);
                
                return response;
                
            } catch (Exception e) {
                logger.error("滤镜预览处理失败: sessionId={}", sessionId, e);
                return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "滤镜预览处理失败", e.getMessage());
            }
        });
    }

    @Override
    @Async
    public CompletableFuture<RealtimeProcessResponse> previewCrop(RealtimeProcessRequest request, String sessionId) {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在裁剪图像...", 10);
                
                if (isCancelled(sessionId)) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "处理已取消", "用户取消了处理");
                }
                
                BufferedImage image = decodeBase64Image(request.getImageData());
                if (image == null) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "图像解码失败", "无效的图像数据");
                }
                
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在处理裁剪...", 50);
                
                // 执行裁剪
                RealtimeProcessRequest.CropParams cropParams = request.getCropParams();
                BufferedImage croppedImage = cropImage(image, cropParams);
                
                if (isCancelled(sessionId)) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "处理已取消", "用户取消了处理");
                }
                
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在生成预览...", 80);
                
                String previewData = encodeImageToBase64(croppedImage, "JPEG");
                
                RealtimeProcessResponse response = RealtimeProcessResponse.success(sessionId, request.getRequestId(), "裁剪预览生成成功");
                response.setPreviewImageData(previewData);
                response.setProcessingTime(System.currentTimeMillis() - startTime);
                response.setImageInfo(new RealtimeProcessResponse.ImageInfo(
                    croppedImage.getWidth(), 
                    croppedImage.getHeight(), 
                    "JPEG", 
                    previewData.length()
                ));
                
                sessionStatus.put(sessionId, response);
                return response;
                
            } catch (Exception e) {
                logger.error("裁剪预览处理失败: sessionId={}", sessionId, e);
                return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "裁剪预览处理失败", e.getMessage());
            }
        });
    }

    @Override
    @Async
    public CompletableFuture<RealtimeProcessResponse> previewAdjustment(RealtimeProcessRequest request, String sessionId) {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在调整图像...", 10);
                
                if (isCancelled(sessionId)) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "处理已取消", "用户取消了处理");
                }
                
                BufferedImage image = decodeBase64Image(request.getImageData());
                if (image == null) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "图像解码失败", "无效的图像数据");
                }
                
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在应用调色...", 50);
                
                // 执行调色
                RealtimeProcessRequest.AdjustParams adjustParams = request.getAdjustParams();
                BufferedImage adjustedImage = adjustImage(image, adjustParams);
                
                if (isCancelled(sessionId)) {
                    return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "处理已取消", "用户取消了处理");
                }
                
                updateProcessingStatus(sessionId, request.getRequestId(), "processing", "正在生成预览...", 80);
                
                String previewData = encodeImageToBase64(adjustedImage, "JPEG");
                
                RealtimeProcessResponse response = RealtimeProcessResponse.success(sessionId, request.getRequestId(), "调色预览生成成功");
                response.setPreviewImageData(previewData);
                response.setProcessingTime(System.currentTimeMillis() - startTime);
                response.setImageInfo(new RealtimeProcessResponse.ImageInfo(
                    adjustedImage.getWidth(), 
                    adjustedImage.getHeight(), 
                    "JPEG", 
                    previewData.length()
                ));
                
                sessionStatus.put(sessionId, response);
                return response;
                
            } catch (Exception e) {
                logger.error("调色预览处理失败: sessionId={}", sessionId, e);
                return RealtimeProcessResponse.error(sessionId, request.getRequestId(), "调色预览处理失败", e.getMessage());
            }
        });
    }

    @Override
    public RealtimeProcessResponse getProcessingStatus(String sessionId) {
        return sessionStatus.getOrDefault(sessionId, 
            RealtimeProcessResponse.error(sessionId, null, "会话不存在", "未找到指定的会话"));
    }

    @Override
    public RealtimeProcessResponse cancelProcessing(String sessionId) {
        AtomicBoolean cancelFlag = sessionCancelFlags.get(sessionId);
        if (cancelFlag != null) {
            cancelFlag.set(true);
            RealtimeProcessResponse response = RealtimeProcessResponse.success(sessionId, null, "处理已取消");
            sessionStatus.put(sessionId, response);
            return response;
        }
        return RealtimeProcessResponse.error(sessionId, null, "取消失败", "会话不存在或已完成");
    }

    @Override
    public void cleanupSession(String sessionId) {
        sessionStatus.remove(sessionId);
        sessionCancelFlags.remove(sessionId);
        logger.debug("清理会话数据: sessionId={}", sessionId);
    }

    // 私有辅助方法
    private void updateProcessingStatus(String sessionId, String requestId, String status, String message, int progress) {
        RealtimeProcessResponse response = RealtimeProcessResponse.processing(sessionId, requestId, message, progress);
        sessionStatus.put(sessionId, response);
    }

    private boolean isCancelled(String sessionId) {
        AtomicBoolean cancelFlag = sessionCancelFlags.computeIfAbsent(sessionId, k -> new AtomicBoolean(false));
        return cancelFlag.get();
    }

    private BufferedImage decodeBase64Image(String base64Data) {
        try {
            // 移除data:image前缀
            String imageData = base64Data;
            if (base64Data.contains(",")) {
                imageData = base64Data.split(",")[1];
            }
            
            byte[] imageBytes = Base64.getDecoder().decode(imageData);
            return ImageIO.read(new ByteArrayInputStream(imageBytes));
        } catch (Exception e) {
            logger.error("图像解码失败", e);
            return null;
        }
    }

    private String encodeImageToBase64(BufferedImage image, String format) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, format, baos);
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 应用滤镜效果
     */
    private BufferedImage applyFilter(BufferedImage image, String filterType) {
        if (filterType == null) {
            return image;
        }

        BufferedImage filteredImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = filteredImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

        switch (filterType.toLowerCase()) {
            case "vintage":
                applyVintageFilter(g2d, image);
                break;
            case "sepia":
                applySepiaFilter(g2d, image);
                break;
            case "grayscale":
                applyGrayscaleFilter(g2d, image);
                break;
            case "blur":
                applyBlurFilter(g2d, image);
                break;
            case "sharpen":
                applySharpenFilter(g2d, image);
                break;
            default:
                g2d.drawImage(image, 0, 0, null);
                break;
        }

        g2d.dispose();
        return filteredImage;
    }

    /**
     * 应用复古滤镜
     */
    private void applyVintageFilter(Graphics2D g2d, BufferedImage image) {
        // 降低饱和度并添加暖色调
        RescaleOp rescaleOp = new RescaleOp(new float[]{0.9f, 0.8f, 0.7f, 1.0f}, new float[]{20, 10, 0, 0}, null);
        BufferedImage vintageImage = rescaleOp.filter(image, null);
        g2d.drawImage(vintageImage, 0, 0, null);

        // 添加暖色叠加
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.1f));
        g2d.setColor(new Color(255, 200, 100));
        g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
    }

    /**
     * 应用棕褐色滤镜
     */
    private void applySepiaFilter(Graphics2D g2d, BufferedImage image) {
        BufferedImage sepiaImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);

        for (int y = 0; y < image.getHeight(); y++) {
            for (int x = 0; x < image.getWidth(); x++) {
                int rgb = image.getRGB(x, y);
                int r = (rgb >> 16) & 0xFF;
                int g = (rgb >> 8) & 0xFF;
                int b = rgb & 0xFF;

                int tr = (int) (0.393 * r + 0.769 * g + 0.189 * b);
                int tg = (int) (0.349 * r + 0.686 * g + 0.168 * b);
                int tb = (int) (0.272 * r + 0.534 * g + 0.131 * b);

                tr = Math.min(255, tr);
                tg = Math.min(255, tg);
                tb = Math.min(255, tb);

                sepiaImage.setRGB(x, y, (tr << 16) | (tg << 8) | tb);
            }
        }

        g2d.drawImage(sepiaImage, 0, 0, null);
    }

    /**
     * 应用灰度滤镜
     */
    private void applyGrayscaleFilter(Graphics2D g2d, BufferedImage image) {
        RescaleOp rescaleOp = new RescaleOp(new float[]{0.299f, 0.587f, 0.114f, 1.0f}, new float[]{0, 0, 0, 0}, null);
        BufferedImage grayImage = rescaleOp.filter(image, null);
        g2d.drawImage(grayImage, 0, 0, null);
    }

    /**
     * 应用模糊滤镜
     */
    private void applyBlurFilter(Graphics2D g2d, BufferedImage image) {
        // 简单的模糊效果
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(image.getScaledInstance(image.getWidth() / 2, image.getHeight() / 2, Image.SCALE_SMOOTH),
                     0, 0, image.getWidth(), image.getHeight(), null);
    }

    /**
     * 应用锐化滤镜
     */
    private void applySharpenFilter(Graphics2D g2d, BufferedImage image) {
        RescaleOp rescaleOp = new RescaleOp(1.2f, 0, null);
        BufferedImage sharpenedImage = rescaleOp.filter(image, null);
        g2d.drawImage(sharpenedImage, 0, 0, null);
    }

    /**
     * 裁剪图像
     */
    private BufferedImage cropImage(BufferedImage image, RealtimeProcessRequest.CropParams cropParams) {
        if (cropParams == null) {
            return image;
        }

        int x = Math.max(0, Math.min(cropParams.getX(), image.getWidth()));
        int y = Math.max(0, Math.min(cropParams.getY(), image.getHeight()));
        int width = Math.min(cropParams.getWidth(), image.getWidth() - x);
        int height = Math.min(cropParams.getHeight(), image.getHeight() - y);

        return image.getSubimage(x, y, width, height);
    }

    /**
     * 调整图像属性
     */
    private BufferedImage adjustImage(BufferedImage image, RealtimeProcessRequest.AdjustParams adjustParams) {
        if (adjustParams == null) {
            return image;
        }

        BufferedImage adjustedImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);

        for (int y = 0; y < image.getHeight(); y++) {
            for (int x = 0; x < image.getWidth(); x++) {
                int rgb = image.getRGB(x, y);
                int r = (rgb >> 16) & 0xFF;
                int g = (rgb >> 8) & 0xFF;
                int b = rgb & 0xFF;

                // 应用亮度调整
                r = adjustBrightness(r, adjustParams.getBrightness());
                g = adjustBrightness(g, adjustParams.getBrightness());
                b = adjustBrightness(b, adjustParams.getBrightness());

                // 应用对比度调整
                r = adjustContrast(r, adjustParams.getContrast());
                g = adjustContrast(g, adjustParams.getContrast());
                b = adjustContrast(b, adjustParams.getContrast());

                // 确保值在有效范围内
                r = Math.max(0, Math.min(255, r));
                g = Math.max(0, Math.min(255, g));
                b = Math.max(0, Math.min(255, b));

                adjustedImage.setRGB(x, y, (r << 16) | (g << 8) | b);
            }
        }

        return adjustedImage;
    }

    private int adjustBrightness(int value, float brightness) {
        return (int) (value + brightness * 255);
    }

    private int adjustContrast(int value, float contrast) {
        return (int) ((value - 128) * (1 + contrast) + 128);
    }
}
