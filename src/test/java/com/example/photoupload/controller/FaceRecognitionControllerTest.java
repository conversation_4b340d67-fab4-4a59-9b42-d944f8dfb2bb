package com.example.photoupload.controller;

import com.example.photoupload.dto.FaceDetectionResult;
import com.example.photoupload.service.SmartAlbumService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class FaceRecognitionControllerTest {

    private MockMvc mockMvc;

    @Mock
    private SmartAlbumService smartAlbumService;

    @InjectMocks
    private FaceRecognitionController faceRecognitionController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(faceRecognitionController).build();
    }

    @Test
    void detectFaces_ShouldReturnDetectionResult() throws Exception {
        // Arrange
        FaceDetectionResult result = new FaceDetectionResult();
        result.setImageId("test-image-1");
        result.setFaceCount(1);

        when(smartAlbumService.analyzeFaces(any(byte[].class))).thenReturn(result);

        byte[] imageContent = Files.readAllBytes(Paths.get("src/test/resources/test-image.jpg"));
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test-image.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                imageContent
        );

        // Act & Assert
        mockMvc.perform(multipart("/api/face-recognition/detect")
                        .file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.imageId").value("test-image-1"))
                .andExpect(jsonPath("$.faceCount").value(1));
    }

    @Test
    void createFaceAlbum_ShouldReturnSuccessMessage() throws Exception {
        // Arrange
        String personName = "John Doe";
        
        // Act & Assert
        mockMvc.perform(post("/api/face-recognition/album")
                        .param("personName", personName))
                .andExpect(status().isOk())
                .andExpect(content().string("人物相册创建成功: " + personName));
    }

    @Test
    void processImageWithFaces_ShouldReturnSuccessMessage() throws Exception {
        // Arrange
        byte[] imageContent = Files.readAllBytes(Paths.get("src/test/resources/test-image.jpg"));
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test-image.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                imageContent
        );

        // Act & Assert
        mockMvc.perform(multipart("/api/face-recognition/process-image")
                        .file(file))
                .andExpect(status().isOk())
                .andExpect(content().string("图片处理完成"));
    }

    @Test
    void detectFaces_WithInvalidImage_ShouldReturnBadRequest() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.txt",
                MediaType.TEXT_PLAIN_VALUE,
                "invalid image content".getBytes()
        );

        // Act & Assert
        mockMvc.perform(multipart("/api/face-recognition/detect")
                        .file(file))
                .andExpect(status().isBadRequest());
    }
}
