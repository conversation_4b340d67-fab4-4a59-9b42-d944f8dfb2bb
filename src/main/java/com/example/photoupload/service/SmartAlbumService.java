package com.example.photoupload.service;

import com.example.photoupload.dto.SmartAlbumRequest;
import com.example.photoupload.dto.SmartAlbumResponse;

import java.util.List;

/**
 * 智能相册服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface SmartAlbumService {

    /**
     * 创建智能相册
     * 
     * @param request 智能相册请求
     * @return 智能相册响应
     */
    SmartAlbumResponse createSmartAlbum(SmartAlbumRequest request);

    /**
     * 获取所有智能相册
     * 
     * @return 智能相册列表
     */
    List<SmartAlbumResponse> getAllSmartAlbums();

    /**
     * 获取智能相册详情
     * 
     * @param albumId 相册ID
     * @return 智能相册响应
     */
    SmartAlbumResponse getSmartAlbum(String albumId);

    /**
     * 更新智能相册
     * 
     * @param albumId 相册ID
     * @param request 更新请求
     * @return 智能相册响应
     */
    SmartAlbumResponse updateSmartAlbum(String albumId, SmartAlbumRequest request);

    /**
     * 删除智能相册
     * 
     * @param albumId 相册ID
     * @return 是否删除成功
     */
    boolean deleteSmartAlbum(String albumId);

    /**
     * 刷新智能相册
     * 
     * @param albumId 相册ID
     * @return 智能相册响应
     */
    SmartAlbumResponse refreshSmartAlbum(String albumId);

    /**
     * 获取智能相册推荐
     * 
     * @return 推荐相册列表
     */
    List<SmartAlbumResponse> getAlbumRecommendations();

    /**
     * 搜索智能相册
     * 
     * @param query 搜索关键词
     * @return 搜索结果
     */
    List<SmartAlbumResponse> searchSmartAlbums(String query);

    /**
     * 获取相册统计信息
     * 
     * @return 统计信息
     */
    SmartAlbumResponse getAlbumStatistics();

    /**
     * 批量操作智能相册
     * 
     * @param requests 批量请求
     * @return 批量响应
     */
    List<SmartAlbumResponse> batchOperations(List<SmartAlbumRequest> requests);

    /**
     * 基于AI分析结果匹配图片
     * 
     * @param criteria 匹配条件
     * @return 匹配的图片列表
     */
    List<SmartAlbumResponse.AlbumImage> matchImagesByCriteria(SmartAlbumRequest.AlbumCriteria criteria);

    /**
     * 分析图片并提取特征
     * 
     * @param fileName 文件名
     * @return AI分析结果
     */
    SmartAlbumResponse.AlbumImage analyzeImageFeatures(String fileName);

    /**
     * 计算图片相似度
     * 
     * @param image1 图片1
     * @param image2 图片2
     * @return 相似度分数
     */
    float calculateImageSimilarity(String image1, String image2);

    /**
     * 自动生成相册名称
     * 
     * @param criteria 分类条件
     * @return 建议的相册名称
     */
    String generateAlbumName(SmartAlbumRequest.AlbumCriteria criteria);

    /**
     * 获取相册缩略图
     * 
     * @param albumId 相册ID
     * @return 缩略图URL
     */
    String generateAlbumThumbnail(String albumId);

    /**
     * 验证相册条件
     * 
     * @param criteria 分类条件
     * @return 是否有效
     */
    boolean validateAlbumCriteria(SmartAlbumRequest.AlbumCriteria criteria);

    /**
     * 优化相册性能
     * 
     * @param albumId 相册ID
     * @return 优化结果
     */
    SmartAlbumResponse optimizeAlbum(String albumId);
}
