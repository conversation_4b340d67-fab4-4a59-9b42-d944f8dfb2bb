package com.example.photoupload.service;

import com.example.photoupload.dto.ThreeDViewRequest;
import com.example.photoupload.dto.ThreeDViewResponse;
import org.springframework.core.io.Resource;

import java.io.IOException;

/**
 * 3D图像服务接口
 * 提供3D图像处理和展示功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface ThreeDImageService {

    /**
     * 生成3D模型
     * 
     * @param request 3D视图请求
     * @return 3D视图响应
     */
    ThreeDViewResponse generate3DModel(ThreeDViewRequest request);

    /**
     * 获取3D模型文件
     * 
     * @param modelId 模型ID
     * @return 模型文件资源
     * @throws IOException IO异常
     */
    Resource get3DModelFile(String modelId) throws IOException;

    /**
     * 获取3D纹理文件
     * 
     * @param modelId 模型ID
     * @return 纹理文件资源
     * @throws IOException IO异常
     */
    Resource get3DTextureFile(String modelId) throws IOException;

    /**
     * 生成全景图
     * 
     * @param request 3D视图请求
     * @return 3D视图响应
     */
    ThreeDViewResponse generatePanorama(ThreeDViewRequest request);

    /**
     * 生成立体图像
     * 
     * @param request 3D视图请求
     * @return 3D视图响应
     */
    ThreeDViewResponse generateStereoscopic(ThreeDViewRequest request);

    /**
     * 获取3D查看器配置
     * 
     * @return 3D视图响应
     */
    ThreeDViewResponse get3DViewerConfig();

    /**
     * 删除3D模型
     * 
     * @param modelId 模型ID
     * @return 是否删除成功
     */
    boolean delete3DModel(String modelId);

    /**
     * 生成深度图
     * 
     * @param fileName 文件名
     * @return 深度图数据
     */
    byte[] generateDepthMap(String fileName);

    /**
     * 创建立方体贴图
     * 
     * @param fileName 文件名
     * @param request 3D视图请求
     * @return 立方体贴图数据
     */
    byte[] createCubeMap(String fileName, ThreeDViewRequest request);

    /**
     * 生成法线贴图
     * 
     * @param fileName 文件名
     * @param intensity 强度
     * @return 法线贴图数据
     */
    byte[] generateNormalMap(String fileName, float intensity);

    /**
     * 创建球形投影
     * 
     * @param fileName 文件名
     * @return 球形投影数据
     */
    byte[] createSphericalProjection(String fileName);

    /**
     * 生成位移贴图
     * 
     * @param fileName 文件名
     * @param heightScale 高度缩放
     * @return 位移贴图数据
     */
    byte[] generateDisplacementMap(String fileName, float heightScale);

    /**
     * 创建环境贴图
     * 
     * @param fileName 文件名
     * @return 环境贴图数据
     */
    byte[] createEnvironmentMap(String fileName);

    /**
     * 验证模型文件
     * 
     * @param modelId 模型ID
     * @return 是否有效
     */
    boolean validateModel(String modelId);

    /**
     * 获取模型统计信息
     * 
     * @param modelId 模型ID
     * @return 统计信息
     */
    ThreeDViewResponse.ModelInfo getModelStatistics(String modelId);

    /**
     * 优化模型
     * 
     * @param modelId 模型ID
     * @param quality 质量等级 (low, medium, high)
     * @return 优化后的模型ID
     */
    String optimizeModel(String modelId, String quality);

    /**
     * 生成模型缩略图
     * 
     * @param modelId 模型ID
     * @param width 宽度
     * @param height 高度
     * @return 缩略图数据
     */
    byte[] generateModelThumbnail(String modelId, int width, int height);
}
