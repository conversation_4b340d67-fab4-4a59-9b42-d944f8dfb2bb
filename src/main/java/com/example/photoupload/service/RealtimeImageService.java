package com.example.photoupload.service;

import com.example.photoupload.dto.RealtimeProcessRequest;
import com.example.photoupload.dto.RealtimeProcessResponse;

import java.util.concurrent.CompletableFuture;

/**
 * 实时图像处理服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface RealtimeImageService {

    /**
     * 实时滤镜预览
     * 
     * @param request 处理请求
     * @param sessionId 会话ID
     * @return 处理结果
     */
    CompletableFuture<RealtimeProcessResponse> previewFilter(RealtimeProcessRequest request, String sessionId);

    /**
     * 实时裁剪预览
     * 
     * @param request 处理请求
     * @param sessionId 会话ID
     * @return 处理结果
     */
    CompletableFuture<RealtimeProcessResponse> previewCrop(RealtimeProcessRequest request, String sessionId);

    /**
     * 实时调色预览
     * 
     * @param request 处理请求
     * @param sessionId 会话ID
     * @return 处理结果
     */
    CompletableFuture<RealtimeProcessResponse> previewAdjustment(RealtimeProcessRequest request, String sessionId);

    /**
     * 获取处理状态
     * 
     * @param sessionId 会话ID
     * @return 处理状态
     */
    RealtimeProcessResponse getProcessingStatus(String sessionId);

    /**
     * 取消处理
     * 
     * @param sessionId 会话ID
     * @return 取消结果
     */
    RealtimeProcessResponse cancelProcessing(String sessionId);

    /**
     * 清理会话数据
     * 
     * @param sessionId 会话ID
     */
    void cleanupSession(String sessionId);
}
