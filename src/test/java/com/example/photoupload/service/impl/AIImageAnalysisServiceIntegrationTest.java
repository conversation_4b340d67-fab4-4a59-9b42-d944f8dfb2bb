package com.example.photoupload.service.impl;

import com.example.photoupload.dto.AIAnalysisResult;
import com.example.photoupload.service.AIImageAnalysisService;
import com.example.photoupload.util.TestDataUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AI图像分析服务集成测试
 * AI Image Analysis Service Integration Test
 *
 * This test class provides comprehensive integration testing for the AI image analysis service,
 * covering complete workflows, individual analysis methods, service availability, and performance testing.
 *
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootTest
@ActiveProfiles("test")
class AIImageAnalysisServiceIntegrationTest {

    // Autowired AI image analysis service for testing
    // 自动注入AI图像分析服务用于测试
    @Autowired
    private AIImageAnalysisService aiImageAnalysisService;

    // Temporary directory for test files
    // 测试文件的临时目录
    @TempDir
    Path tempDir;

    // Test image file for analysis
    // 用于分析的测试图像文件
    private File testImageFile;

    // Mock multipart file for upload testing
    // 用于上传测试的模拟多部分文件
    private MockMultipartFile testMultipartFile;

    /**
     * Set up test environment before each test
     * 在每个测试前设置测试环境
     */
    @BeforeEach
    void setUp() throws IOException {
        // 创建测试图像文件
        // Create test image file with shapes for comprehensive analysis
        testImageFile = TestDataUtils.createTestImageWithShapes(tempDir, "integration-test.jpg");

        // 创建测试 MultipartFile
        // Create test MultipartFile for upload simulation
        testMultipartFile = TestDataUtils.createImageMultipartFile("integration-test.jpg", 150, 150);
    }

    /**
     * 测试完整的图像分析工作流程
     * Test complete image analysis workflow
     *
     * 验证从输入图像文件到输出完整分析结果的整个流程，这个是我新加的test文案
     * Validates the entire process from input image file to complete analysis results output.
     * This comprehensive test ensures all AI analysis components work together correctly.
     */
    @Test
    void testCompleteAnalysisWorkflow() throws ExecutionException, InterruptedException {
        // When - 执行完整的图像分析
        // When - Execute complete image analysis
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
        AIAnalysisResult result = future.get();

        // Then - 验证分析结果
        // Then - Validate analysis results
        assertNotNull(result, "分析结果不能为空 / Analysis result cannot be null");
        TestDataUtils.validateAnalysisResult(result);

        // 验证各个分析组件都有结果
        // Verify that all analysis components have results
        assertNotNull(result.getLabels(), "标签分析结果不能为空 / Label analysis result cannot be null");
        assertNotNull(result.getObjects(), "物体检测结果不能为空 / Object detection result cannot be null");
        assertNotNull(result.getFaces(), "人脸检测结果不能为空 / Face detection result cannot be null");
        assertNotNull(result.getTextDetections(), "文字识别结果不能为空 / Text recognition result cannot be null");
        assertNotNull(result.getColorAnalysis(), "颜色分析结果不能为空 / Color analysis result cannot be null");
        assertNotNull(result.getMetadata(), "元数据不能为空 / Metadata cannot be null");

        // 验证处理时间合理
        // Verify reasonable processing time
        assertTrue(result.getProcessingTime() > 0, "处理时间应该大于0 / Processing time should be greater than 0");
        assertTrue(result.getProcessingTime() < 10000, "处理时间不应该超过10秒 / Processing time should not exceed 10 seconds");

        // 验证置信度合理
        // Verify reasonable confidence levels
        assertTrue(result.getOverallConfidence() >= 0.8, "整体置信度应该较高 / Overall confidence should be high");
        assertTrue(result.getOverallConfidence() <= 1.0, "整体置信度不能超过1.0 / Overall confidence cannot exceed 1.0");
    }

    /**
     * 测试MultipartFile文件分析工作流程
     * Test MultipartFile analysis workflow
     *
     * 验证通过MultipartFile上传的文件能够正确进行AI分析
     * Validates that files uploaded via MultipartFile can be correctly analyzed by AI
     */
    @Test
    void testMultipartFileAnalysisWorkflow() throws ExecutionException, InterruptedException {
        // When - 使用 MultipartFile 进行分析
        // When - Analyze using MultipartFile
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testMultipartFile);
        AIAnalysisResult result = future.get();

        // Then - 验证分析结果
        // Then - Validate analysis results
        assertNotNull(result, "分析结果不能为空 / Analysis result cannot be null");
        TestDataUtils.validateAnalysisResult(result);

        // 验证临时文件被正确清理（通过检查结果是否正常生成来间接验证）
        // Verify temporary files are properly cleaned up (indirectly verified by checking if results are generated normally)
        assertNotNull(result.getMetadata(), "元数据应该包含文件信息 / Metadata should contain file information");
    }

    /**
     * 测试各个独立的分析方法
     * Test individual analysis methods
     *
     * 验证物体检测、人脸检测、文字识别、颜色分析、标签生成等单独功能
     * Validates individual functions like object detection, face detection, text recognition, color analysis, and label generation
     */
    @Test
    void testIndividualAnalysisMethods() throws ExecutionException, InterruptedException {
        // Test object detection - 测试物体检测
        CompletableFuture<AIAnalysisResult> objectsFuture = aiImageAnalysisService.detectObjects(testImageFile);
        AIAnalysisResult objectsResult = objectsFuture.get();
        assertNotNull(objectsResult, "Object detection result should not be null");
        assertNotNull(objectsResult.getObjects(), "Detected objects list should not be null");

        // Test face detection - 测试人脸检测
        CompletableFuture<AIAnalysisResult> facesFuture = aiImageAnalysisService.detectFaces(testImageFile);
        AIAnalysisResult facesResult = facesFuture.get();
        assertNotNull(facesResult, "Face detection result should not be null");
        assertNotNull(facesResult.getFaces(), "Detected faces list should not be null");

        // Test text recognition - 测试文字识别
        CompletableFuture<AIAnalysisResult> textFuture = aiImageAnalysisService.recognizeText(testImageFile);
        AIAnalysisResult textResult = textFuture.get();
        assertNotNull(textResult, "Text recognition result should not be null");
        assertNotNull(textResult.getTextDetections(), "Text detections list should not be null");

        // Test color analysis - 测试颜色分析
        CompletableFuture<AIAnalysisResult> colorsFuture = aiImageAnalysisService.analyzeColors(testImageFile);
        AIAnalysisResult colorsResult = colorsFuture.get();
        assertNotNull(colorsResult, "Color analysis result should not be null");
        assertNotNull(colorsResult.getColorAnalysis(), "Color analysis data should not be null");

        // Test label generation - 测试标签生成
        CompletableFuture<AIAnalysisResult> labelsFuture = aiImageAnalysisService.generateLabels(testImageFile);
        AIAnalysisResult labelsResult = labelsFuture.get();
        assertNotNull(labelsResult, "Label generation result should not be null");
        assertNotNull(labelsResult.getLabels(), "Generated labels list should not be null");
    }

    /**
     * 测试AI服务的可用性
     * Test AI service availability
     *
     * 验证服务状态检查和支持的文件格式列表功能
     * Validates service status checking and supported file format listing functionality
     */
    @Test
    void testServiceAvailability() {
        // When - 检查服务可用性和支持格式
        // When - Check service availability and supported formats
        boolean available = aiImageAnalysisService.isServiceAvailable();
        String[] formats = aiImageAnalysisService.getSupportedFormats();

        // Then - 验证服务状态和格式支持
        // Then - Validate service status and format support
        assertTrue(available, "AI服务应该可用 / AI service should be available");
        assertNotNull(formats, "支持的格式列表不能为空 / Supported formats list cannot be null");
        assertTrue(formats.length > 0, "应该支持至少一种格式 / Should support at least one format");

        // 验证常见格式都被支持
        // Verify that common formats are supported
        boolean supportsJpg = false;
        boolean supportsPng = false;
        for (String format : formats) {
            if ("jpg".equals(format) || "jpeg".equals(format)) {
                supportsJpg = true;
            }
            if ("png".equals(format)) {
                supportsPng = true;
            }
        }
        assertTrue(supportsJpg, "应该支持JPG格式 / Should support JPG format");
        assertTrue(supportsPng, "应该支持PNG格式 / Should support PNG format");
    }

    /**
     * 测试不同尺寸图像的处理能力
     * Test processing capability for different image sizes
     *
     * 验证小图像和大图像都能正确处理和分析
     * Validates that both small and large images can be correctly processed and analyzed
     */
    @Test
    void testDifferentImageSizes() throws IOException, ExecutionException, InterruptedException {
        // Test small image - 测试小图像
        File smallImage = TestDataUtils.createTestImageFile(tempDir, "small.jpg", 50, 50, Color.RED);
        CompletableFuture<AIAnalysisResult> smallFuture = aiImageAnalysisService.analyzeImage(smallImage);
        AIAnalysisResult smallResult = smallFuture.get();
        assertNotNull(smallResult, "Small image analysis result should not be null");
        TestDataUtils.validateAnalysisResult(smallResult);

        // Test large image - 测试大图像
        File largeImage = TestDataUtils.createTestImageFile(tempDir, "large.jpg", 1000, 1000, Color.BLUE);
        CompletableFuture<AIAnalysisResult> largeFuture = aiImageAnalysisService.analyzeImage(largeImage);
        AIAnalysisResult largeResult = largeFuture.get();
        assertNotNull(largeResult, "Large image analysis result should not be null");
        TestDataUtils.validateAnalysisResult(largeResult);

        // 验证不同尺寸的图像都能正确处理
        // Verify that images of different sizes can be processed correctly
        assertTrue(smallResult.getProcessingTime() > 0, "Small image processing time should be positive");
        assertTrue(largeResult.getProcessingTime() > 0, "Large image processing time should be positive");
    }

    /**
     * 测试并发分析处理能力
     * Test concurrent analysis processing capability
     *
     * 验证多个分析任务同时执行时的正确性和稳定性
     * Validates correctness and stability when multiple analysis tasks are executed simultaneously
     */
    @Test
    void testConcurrentAnalysis() throws InterruptedException {
        // Given - 创建多个并发分析任务
        // Given - Create multiple concurrent analysis tasks
        int numberOfTasks = 3;
        @SuppressWarnings("unchecked")
        CompletableFuture<AIAnalysisResult>[] futures = new CompletableFuture[numberOfTasks];

        // When - 并发执行分析
        // When - Execute analysis concurrently
        for (int i = 0; i < numberOfTasks; i++) {
            futures[i] = aiImageAnalysisService.analyzeImage(testImageFile);
        }

        // Then - 等待所有任务完成并验证结果
        // Then - Wait for all tasks to complete and validate results
        for (CompletableFuture<AIAnalysisResult> future : futures) {
            AIAnalysisResult result = future.join();
            assertNotNull(result, "Concurrent analysis result should not be null");
            TestDataUtils.validateAnalysisResult(result);
        }
    }

    /**
     * 测试分析结果的一致性
     * Test analysis result consistency
     *
     * 验证对同一图像多次分析的结果具有一致的结构和合理的数值
     * Validates that multiple analyses of the same image produce results with consistent structure and reasonable values
     */
    @Test
    void testAnalysisResultConsistency() throws ExecutionException, InterruptedException {
        // Given - 对同一图像执行多次分析
        // Given - Execute multiple analyses on the same image
        CompletableFuture<AIAnalysisResult> future1 = aiImageAnalysisService.analyzeImage(testImageFile);
        CompletableFuture<AIAnalysisResult> future2 = aiImageAnalysisService.analyzeImage(testImageFile);

        AIAnalysisResult result1 = future1.get();
        AIAnalysisResult result2 = future2.get();

        // Then - 验证结果的一致性（在模拟模式下，结果应该具有相似的结构）
        // Then - Validate result consistency (in mock mode, results should have similar structure)
        assertNotNull(result1, "First analysis result should not be null");
        assertNotNull(result2, "Second analysis result should not be null");

        // 验证两次分析都产生了有效结果
        // Verify that both analyses produced valid results
        TestDataUtils.validateAnalysisResult(result1);
        TestDataUtils.validateAnalysisResult(result2);

        // 验证结果结构一致性
        // Verify result structure consistency
        assertEquals(result1.getLabels().size() >= 3, result2.getLabels().size() >= 3,
                    "Both results should have consistent label count structure");
        assertEquals(result1.getObjects().size() >= 1, result2.getObjects().size() >= 1,
                    "Both results should have consistent object count structure");

        // 验证置信度都在合理范围内
        // Verify that confidence levels are within reasonable range
        assertTrue(result1.getOverallConfidence() >= 0.8, "First result confidence should be high");
        assertTrue(result2.getOverallConfidence() >= 0.8, "Second result confidence should be high");
    }

    /**
     * 测试元数据的完整性
     * Test metadata integrity
     *
     * 验证分析结果中包含正确和完整的元数据信息
     * Validates that analysis results contain correct and complete metadata information
     */
    @Test
    void testMetadataIntegrity() throws ExecutionException, InterruptedException {
        // When - 执行图像分析
        // When - Execute image analysis
        CompletableFuture<AIAnalysisResult> future = aiImageAnalysisService.analyzeImage(testImageFile);
        AIAnalysisResult result = future.get();

        // Then - 验证元数据的完整性
        // Then - Validate metadata integrity
        assertNotNull(result.getMetadata(), "Metadata should not be null");

        // 验证必要的元数据字段
        // Verify required metadata fields
        assertTrue(result.getMetadata().containsKey("file_name"), "Metadata should contain file_name");
        assertTrue(result.getMetadata().containsKey("file_size"), "Metadata should contain file_size");
        assertTrue(result.getMetadata().containsKey("analysis_time"), "Metadata should contain analysis_time");
        assertTrue(result.getMetadata().containsKey("model_version"), "Metadata should contain model_version");

        // 验证元数据值的正确性
        // Verify correctness of metadata values
        assertEquals(testImageFile.getName(), result.getMetadata().get("file_name"),
                    "File name in metadata should match test file name");
        assertEquals(testImageFile.length(), result.getMetadata().get("file_size"),
                    "File size in metadata should match test file size");
        assertEquals("mock-v1.0", result.getMetadata().get("model_version"),
                    "Model version should match expected mock version");

        // 验证分析时间格式
        // Verify analysis time format
        assertNotNull(result.getMetadata().get("analysis_time"), "Analysis time should not be null");
        assertTrue(result.getMetadata().get("analysis_time") instanceof String,
                  "Analysis time should be a string");
    }
}
