package com.example.photoupload.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 图像搜索响应DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "图像搜索响应")
public class ImageSearchResponse {

    @Schema(description = "是否成功", example = "true")
    private boolean success;

    @Schema(description = "响应消息", example = "搜索成功")
    private String message;

    @Schema(description = "搜索结果")
    private List<SearchResult> results;

    @Schema(description = "总结果数", example = "150")
    private long totalCount;

    @Schema(description = "搜索耗时（毫秒）", example = "245")
    private long searchTime;

    @Schema(description = "搜索ID", example = "search_123456")
    private String searchId;

    @Schema(description = "搜索统计信息")
    private SearchStats stats;

    @Schema(description = "相关搜索建议")
    private List<String> relatedQueries;

    @Schema(description = "搜索聚合信息")
    private SearchAggregations aggregations;

    @Schema(description = "附加数据")
    private Map<String, Object> data;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "搜索结果项")
    public static class SearchResult {
        @Schema(description = "图片ID", example = "img_123")
        private String imageId;

        @Schema(description = "图片URL", example = "/api/files/img_123.jpg")
        private String imageUrl;

        @Schema(description = "缩略图URL", example = "/api/files/thumbnails/img_123.jpg")
        private String thumbnailUrl;

        @Schema(description = "图片标题", example = "美丽的日落")
        private String title;

        @Schema(description = "图片描述", example = "海边的美丽日落景色")
        private String description;

        @Schema(description = "相似度分数", example = "0.95")
        private double similarityScore;

        @Schema(description = "相关性分数", example = "0.88")
        private double relevanceScore;

        @Schema(description = "图片元数据")
        private ImageMetadata metadata;

        @Schema(description = "AI分析结果")
        private AiAnalysisResult aiAnalysis;

        @Schema(description = "用户信息")
        private UserInfo userInfo;

        @Schema(description = "标签")
        private List<String> tags;

        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        @Schema(description = "最后修改时间")
        private LocalDateTime updatedAt;

        // Getters and Setters
        public String getImageId() { return imageId; }
        public void setImageId(String imageId) { this.imageId = imageId; }

        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }

        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public double getSimilarityScore() { return similarityScore; }
        public void setSimilarityScore(double similarityScore) { this.similarityScore = similarityScore; }

        public double getRelevanceScore() { return relevanceScore; }
        public void setRelevanceScore(double relevanceScore) { this.relevanceScore = relevanceScore; }

        public ImageMetadata getMetadata() { return metadata; }
        public void setMetadata(ImageMetadata metadata) { this.metadata = metadata; }

        public AiAnalysisResult getAiAnalysis() { return aiAnalysis; }
        public void setAiAnalysis(AiAnalysisResult aiAnalysis) { this.aiAnalysis = aiAnalysis; }

        public UserInfo getUserInfo() { return userInfo; }
        public void setUserInfo(UserInfo userInfo) { this.userInfo = userInfo; }

        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }

        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

        public LocalDateTime getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    }

    @Schema(description = "图片元数据")
    public static class ImageMetadata {
        @Schema(description = "文件名", example = "sunset.jpg")
        private String fileName;

        @Schema(description = "文件大小", example = "2048576")
        private long fileSize;

        @Schema(description = "图片宽度", example = "1920")
        private int width;

        @Schema(description = "图片高度", example = "1080")
        private int height;

        @Schema(description = "文件格式", example = "JPEG")
        private String format;

        @Schema(description = "颜色空间", example = "RGB")
        private String colorSpace;

        @Schema(description = "主要颜色")
        private List<String> dominantColors;

        @Schema(description = "EXIF信息")
        private Map<String, Object> exifData;

        @Schema(description = "地理位置信息")
        private LocationInfo locationInfo;

        // Getters and Setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }

        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }

        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }

        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }

        public String getColorSpace() { return colorSpace; }
        public void setColorSpace(String colorSpace) { this.colorSpace = colorSpace; }

        public List<String> getDominantColors() { return dominantColors; }
        public void setDominantColors(List<String> dominantColors) { this.dominantColors = dominantColors; }

        public Map<String, Object> getExifData() { return exifData; }
        public void setExifData(Map<String, Object> exifData) { this.exifData = exifData; }

        public LocationInfo getLocationInfo() { return locationInfo; }
        public void setLocationInfo(LocationInfo locationInfo) { this.locationInfo = locationInfo; }
    }

    @Schema(description = "AI分析结果")
    public static class AiAnalysisResult {
        @Schema(description = "检测到的物体")
        private List<DetectedObject> detectedObjects;

        @Schema(description = "检测到的人脸")
        private List<DetectedFace> detectedFaces;

        @Schema(description = "场景分类")
        private List<SceneClassification> sceneClassifications;

        @Schema(description = "情感分析")
        private EmotionAnalysis emotionAnalysis;

        @Schema(description = "文本识别结果")
        private List<String> recognizedTexts;

        @Schema(description = "图像质量评分", example = "8.5")
        private double qualityScore;

        @Schema(description = "美学评分", example = "7.8")
        private double aestheticScore;

        // Getters and Setters
        public List<DetectedObject> getDetectedObjects() { return detectedObjects; }
        public void setDetectedObjects(List<DetectedObject> detectedObjects) { this.detectedObjects = detectedObjects; }

        public List<DetectedFace> getDetectedFaces() { return detectedFaces; }
        public void setDetectedFaces(List<DetectedFace> detectedFaces) { this.detectedFaces = detectedFaces; }

        public List<SceneClassification> getSceneClassifications() { return sceneClassifications; }
        public void setSceneClassifications(List<SceneClassification> sceneClassifications) { this.sceneClassifications = sceneClassifications; }

        public EmotionAnalysis getEmotionAnalysis() { return emotionAnalysis; }
        public void setEmotionAnalysis(EmotionAnalysis emotionAnalysis) { this.emotionAnalysis = emotionAnalysis; }

        public List<String> getRecognizedTexts() { return recognizedTexts; }
        public void setRecognizedTexts(List<String> recognizedTexts) { this.recognizedTexts = recognizedTexts; }

        public double getQualityScore() { return qualityScore; }
        public void setQualityScore(double qualityScore) { this.qualityScore = qualityScore; }

        public double getAestheticScore() { return aestheticScore; }
        public void setAestheticScore(double aestheticScore) { this.aestheticScore = aestheticScore; }
    }

    @Schema(description = "检测到的物体")
    public static class DetectedObject {
        @Schema(description = "物体名称", example = "cat")
        private String name;

        @Schema(description = "置信度", example = "0.95")
        private double confidence;

        @Schema(description = "边界框")
        private BoundingBox boundingBox;

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public double getConfidence() { return confidence; }
        public void setConfidence(double confidence) { this.confidence = confidence; }

        public BoundingBox getBoundingBox() { return boundingBox; }
        public void setBoundingBox(BoundingBox boundingBox) { this.boundingBox = boundingBox; }
    }

    @Schema(description = "检测到的人脸")
    public static class DetectedFace {
        @Schema(description = "人脸ID", example = "face_123")
        private String faceId;

        @Schema(description = "置信度", example = "0.98")
        private double confidence;

        @Schema(description = "年龄估计", example = "25")
        private int estimatedAge;

        @Schema(description = "性别", example = "female")
        private String gender;

        @Schema(description = "情感", example = "happy")
        private String emotion;

        @Schema(description = "边界框")
        private BoundingBox boundingBox;

        @Schema(description = "人脸特征向量")
        private List<Double> faceEmbedding;

        // Getters and Setters
        public String getFaceId() { return faceId; }
        public void setFaceId(String faceId) { this.faceId = faceId; }

        public double getConfidence() { return confidence; }
        public void setConfidence(double confidence) { this.confidence = confidence; }

        public int getEstimatedAge() { return estimatedAge; }
        public void setEstimatedAge(int estimatedAge) { this.estimatedAge = estimatedAge; }

        public String getGender() { return gender; }
        public void setGender(String gender) { this.gender = gender; }

        public String getEmotion() { return emotion; }
        public void setEmotion(String emotion) { this.emotion = emotion; }

        public BoundingBox getBoundingBox() { return boundingBox; }
        public void setBoundingBox(BoundingBox boundingBox) { this.boundingBox = boundingBox; }

        public List<Double> getFaceEmbedding() { return faceEmbedding; }
        public void setFaceEmbedding(List<Double> faceEmbedding) { this.faceEmbedding = faceEmbedding; }
    }

    @Schema(description = "边界框")
    public static class BoundingBox {
        @Schema(description = "X坐标", example = "100")
        private int x;

        @Schema(description = "Y坐标", example = "150")
        private int y;

        @Schema(description = "宽度", example = "200")
        private int width;

        @Schema(description = "高度", example = "250")
        private int height;

        public int getX() { return x; }
        public void setX(int x) { this.x = x; }

        public int getY() { return y; }
        public void setY(int y) { this.y = y; }

        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }

        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
    }

    @Schema(description = "场景分类")
    public static class SceneClassification {
        @Schema(description = "场景名称", example = "beach")
        private String sceneName;

        @Schema(description = "置信度", example = "0.92")
        private double confidence;

        public String getSceneName() { return sceneName; }
        public void setSceneName(String sceneName) { this.sceneName = sceneName; }

        public double getConfidence() { return confidence; }
        public void setConfidence(double confidence) { this.confidence = confidence; }
    }

    @Schema(description = "情感分析")
    public static class EmotionAnalysis {
        @Schema(description = "主要情感", example = "joy")
        private String primaryEmotion;

        @Schema(description = "情感分数")
        private Map<String, Double> emotionScores;

        @Schema(description = "整体情感极性", example = "positive")
        private String overallSentiment;

        public String getPrimaryEmotion() { return primaryEmotion; }
        public void setPrimaryEmotion(String primaryEmotion) { this.primaryEmotion = primaryEmotion; }

        public Map<String, Double> getEmotionScores() { return emotionScores; }
        public void setEmotionScores(Map<String, Double> emotionScores) { this.emotionScores = emotionScores; }

        public String getOverallSentiment() { return overallSentiment; }
        public void setOverallSentiment(String overallSentiment) { this.overallSentiment = overallSentiment; }
    }

    @Schema(description = "地理位置信息")
    public static class LocationInfo {
        @Schema(description = "纬度", example = "39.9042")
        private double latitude;

        @Schema(description = "经度", example = "116.4074")
        private double longitude;

        @Schema(description = "城市", example = "北京")
        private String city;

        @Schema(description = "国家", example = "中国")
        private String country;

        @Schema(description = "地址", example = "北京市朝阳区")
        private String address;

        public double getLatitude() { return latitude; }
        public void setLatitude(double latitude) { this.latitude = latitude; }

        public double getLongitude() { return longitude; }
        public void setLongitude(double longitude) { this.longitude = longitude; }

        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }

        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }

        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
    }

    @Schema(description = "用户信息")
    public static class UserInfo {
        @Schema(description = "用户ID", example = "user123")
        private String userId;

        @Schema(description = "用户名", example = "张三")
        private String username;

        @Schema(description = "头像URL", example = "/api/files/avatars/user123.jpg")
        private String avatar;

        @Schema(description = "是否已验证", example = "true")
        private boolean verified;

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }

        public boolean isVerified() { return verified; }
        public void setVerified(boolean verified) { this.verified = verified; }
    }

    @Schema(description = "搜索统计信息")
    public static class SearchStats {
        @Schema(description = "索引图片总数", example = "1000000")
        private long totalIndexedImages;

        @Schema(description = "搜索命中数", example = "150")
        private long searchHits;

        @Schema(description = "平均相似度", example = "0.85")
        private double averageSimilarity;

        @Schema(description = "搜索覆盖率", example = "0.95")
        private double searchCoverage;

        @Schema(description = "性能指标")
        private Map<String, Object> performanceMetrics;

        public long getTotalIndexedImages() { return totalIndexedImages; }
        public void setTotalIndexedImages(long totalIndexedImages) { this.totalIndexedImages = totalIndexedImages; }

        public long getSearchHits() { return searchHits; }
        public void setSearchHits(long searchHits) { this.searchHits = searchHits; }

        public double getAverageSimilarity() { return averageSimilarity; }
        public void setAverageSimilarity(double averageSimilarity) { this.averageSimilarity = averageSimilarity; }

        public double getSearchCoverage() { return searchCoverage; }
        public void setSearchCoverage(double searchCoverage) { this.searchCoverage = searchCoverage; }

        public Map<String, Object> getPerformanceMetrics() { return performanceMetrics; }
        public void setPerformanceMetrics(Map<String, Object> performanceMetrics) { this.performanceMetrics = performanceMetrics; }
    }

    @Schema(description = "搜索聚合信息")
    public static class SearchAggregations {
        @Schema(description = "按文件类型聚合")
        private Map<String, Long> fileTypeAggregation;

        @Schema(description = "按颜色聚合")
        private Map<String, Long> colorAggregation;

        @Schema(description = "按用户聚合")
        private Map<String, Long> userAggregation;

        @Schema(description = "按标签聚合")
        private Map<String, Long> tagAggregation;

        @Schema(description = "按时间聚合")
        private Map<String, Long> timeAggregation;

        public Map<String, Long> getFileTypeAggregation() { return fileTypeAggregation; }
        public void setFileTypeAggregation(Map<String, Long> fileTypeAggregation) { this.fileTypeAggregation = fileTypeAggregation; }

        public Map<String, Long> getColorAggregation() { return colorAggregation; }
        public void setColorAggregation(Map<String, Long> colorAggregation) { this.colorAggregation = colorAggregation; }

        public Map<String, Long> getUserAggregation() { return userAggregation; }
        public void setUserAggregation(Map<String, Long> userAggregation) { this.userAggregation = userAggregation; }

        public Map<String, Long> getTagAggregation() { return tagAggregation; }
        public void setTagAggregation(Map<String, Long> tagAggregation) { this.tagAggregation = tagAggregation; }

        public Map<String, Long> getTimeAggregation() { return timeAggregation; }
        public void setTimeAggregation(Map<String, Long> timeAggregation) { this.timeAggregation = timeAggregation; }
    }

    @Schema(description = "搜索历史")
    public static class SearchHistory {
        @Schema(description = "搜索ID", example = "search_123")
        private String searchId;

        @Schema(description = "搜索查询", example = "美丽的风景")
        private String query;

        @Schema(description = "搜索类型", example = "semantic")
        private String searchType;

        @Schema(description = "结果数量", example = "25")
        private int resultCount;

        @Schema(description = "搜索时间")
        private LocalDateTime searchTime;

        @Schema(description = "搜索耗时（毫秒）", example = "150")
        private long duration;

        public String getSearchId() { return searchId; }
        public void setSearchId(String searchId) { this.searchId = searchId; }

        public String getQuery() { return query; }
        public void setQuery(String query) { this.query = query; }

        public String getSearchType() { return searchType; }
        public void setSearchType(String searchType) { this.searchType = searchType; }

        public int getResultCount() { return resultCount; }
        public void setResultCount(int resultCount) { this.resultCount = resultCount; }

        public LocalDateTime getSearchTime() { return searchTime; }
        public void setSearchTime(LocalDateTime searchTime) { this.searchTime = searchTime; }

        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
    }

    @Schema(description = "保存的搜索")
    public static class SavedSearch {
        @Schema(description = "搜索ID", example = "saved_123")
        private String searchId;

        @Schema(description = "搜索名称", example = "我的风景搜索")
        private String searchName;

        @Schema(description = "搜索查询", example = "美丽的风景")
        private String query;

        @Schema(description = "搜索类型", example = "semantic")
        private String searchType;

        @Schema(description = "搜索过滤器")
        private Map<String, Object> filters;

        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        @Schema(description = "最后使用时间")
        private LocalDateTime lastUsedAt;

        public String getSearchId() { return searchId; }
        public void setSearchId(String searchId) { this.searchId = searchId; }

        public String getSearchName() { return searchName; }
        public void setSearchName(String searchName) { this.searchName = searchName; }

        public String getQuery() { return query; }
        public void setQuery(String query) { this.query = query; }

        public String getSearchType() { return searchType; }
        public void setSearchType(String searchType) { this.searchType = searchType; }

        public Map<String, Object> getFilters() { return filters; }
        public void setFilters(Map<String, Object> filters) { this.filters = filters; }

        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

        public LocalDateTime getLastUsedAt() { return lastUsedAt; }
        public void setLastUsedAt(LocalDateTime lastUsedAt) { this.lastUsedAt = lastUsedAt; }
    }

    // 静态工厂方法
    public static ImageSearchResponse success(String message) {
        ImageSearchResponse response = new ImageSearchResponse();
        response.setSuccess(true);
        response.setMessage(message);
        response.setCreatedAt(LocalDateTime.now());
        return response;
    }

    public static ImageSearchResponse error(String message) {
        ImageSearchResponse response = new ImageSearchResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setCreatedAt(LocalDateTime.now());
        return response;
    }

    // Main class getters and setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public List<SearchResult> getResults() { return results; }
    public void setResults(List<SearchResult> results) { this.results = results; }

    public long getTotalCount() { return totalCount; }
    public void setTotalCount(long totalCount) { this.totalCount = totalCount; }

    public long getSearchTime() { return searchTime; }
    public void setSearchTime(long searchTime) { this.searchTime = searchTime; }

    public String getSearchId() { return searchId; }
    public void setSearchId(String searchId) { this.searchId = searchId; }

    public SearchStats getStats() { return stats; }
    public void setStats(SearchStats stats) { this.stats = stats; }

    public List<String> getRelatedQueries() { return relatedQueries; }
    public void setRelatedQueries(List<String> relatedQueries) { this.relatedQueries = relatedQueries; }

    public SearchAggregations getAggregations() { return aggregations; }
    public void setAggregations(SearchAggregations aggregations) { this.aggregations = aggregations; }

    public Map<String, Object> getData() { return data; }
    public void setData(Map<String, Object> data) { this.data = data; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
}
