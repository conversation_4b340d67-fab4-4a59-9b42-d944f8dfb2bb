package com.example.photoupload.controller;

import com.example.photoupload.dto.FaceDetectionResult;
import com.example.photoupload.service.SmartAlbumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 人脸识别控制器
 */
@RestController
@RequestMapping("/api/face-recognition")
public class FaceRecognitionController {

    private final SmartAlbumService smartAlbumService;

    @Autowired
    public FaceRecognitionController(SmartAlbumService smartAlbumService) {
        this.smartAlbumService = smartAlbumService;
    }

    /**
     * 分析图片中的人脸
     * @param file 图片文件
     * @return 人脸检测结果
     */
    @PostMapping(value = "/detect", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<FaceDetectionResult> detectFaces(@RequestParam("file") MultipartFile file) {
        try {
            byte[] imageData = file.getBytes();
            FaceDetectionResult result = smartAlbumService.analyzeFaces(imageData);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 创建基于人脸的相册
     * @param personName 人物名称
     * @return 创建结果
     */
    @PostMapping("/album")
    public ResponseEntity<String> createFaceAlbum(@RequestParam String personName) {
        try {
            smartAlbumService.createFaceAlbum(personName);
            return ResponseEntity.ok("人物相册创建成功: " + personName);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("创建人物相册失败: " + e.getMessage());
        }
    }

    /**
     * 处理新上传的图片（包含人脸检测）
     * @param file 图片文件
     * @return 处理结果
     */
    @PostMapping(value = "/process-image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> processImageWithFaces(@RequestParam("file") MultipartFile file) {
        try {
            byte[] imageData = file.getBytes();
            smartAlbumService.processNewImage(imageData, file.getOriginalFilename());
            return ResponseEntity.ok("图片处理完成");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("图片处理失败: " + e.getMessage());
        }
    }
}
