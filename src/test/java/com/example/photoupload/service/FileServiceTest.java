package com.example.photoupload.service;

import com.example.photoupload.dto.FileUploadResponse;
import com.example.photoupload.entity.FileInfo;
import com.example.photoupload.repository.FileInfoRepository;
import com.example.photoupload.service.impl.FileServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 文件服务测试类
 *
 * 该测试类用于测试FileService接口的各种功能，包括：
 * - 文件上传功能
 * - 重复文件检测
 * - 文件存在性检查
 * - 文件删除功能
 *
 * 使用Mockito框架进行单元测试，模拟依赖的Repository和Service
 *
 * <AUTHOR>
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class FileServiceTest {

    /**
     * 模拟文件信息数据访问层
     * 用于模拟数据库操作，如保存、查询、删除文件信息
     */
    @Mock
    private FileInfoRepository fileInfoRepository;

    /**
     * 模拟图片处理服务
     * 用于模拟图片处理相关的业务逻辑
     */
    @Mock
    private ImageProcessingService imageProcessingService;

    /**
     * 被测试的文件服务实现类
     * 通过@InjectMocks注解自动注入模拟的依赖对象
     */
    @InjectMocks
    private FileServiceImpl fileService;

    /**
     * 测试用的模拟文件对象
     * 在每个测试方法执行前初始化
     */
    private MockMultipartFile testFile;

    /**
     * 测试前的初始化方法
     * 在每个测试方法执行前都会调用此方法
     *
     * 创建一个模拟的MultipartFile对象用于测试
     * 包含测试所需的基本文件信息：文件名、MIME类型、文件内容等
     */
    @BeforeEach
    void setUp() {
        testFile = new MockMultipartFile(
                "file",           // 表单字段名
                "test.jpg",       // 原始文件名
                "image/jpeg",     // MIME类型
                "test image content".getBytes()  // 文件内容（字节数组）
        );哦哦哦、、】、、、】
    }

    /**
     * 测试文件上传成功的场景
     *
     * 测试条件：
     * - 上传的文件不存在重复（MD5哈希值不重复）
     * - 文件信息能够成功保存到数据库
     *
     * 验证点：
     * - 返回的响应对象不为空
     * - 原始文件名正确
     * - 存储文件名不为空
     * - 文件大小正确
     * - 数据库保存操作被调用一次
     */
    @Test
    void testUploadFile_Success() throws IOException {
        // Given - 准备测试数据和模拟行为
        // 模拟数据库中不存在相同MD5哈希值的文件
        when(fileInfoRepository.findByMd5HashAndIsDeletedFalse(any())).thenReturn(Optional.empty());
        // 模拟数据库保存操作，返回带有ID的文件信息对象
        when(fileInfoRepository.save(any(FileInfo.class))).thenAnswer(invocation -> {
            FileInfo fileInfo = invocation.getArgument(0);
            fileInfo.setId(1L);  // 模拟数据库自动生成的ID
            return fileInfo;
        });

        // When - 执行被测试的方法
        FileUploadResponse response = fileService.uploadFile(testFile, "127.0.0.1");

        // Then - 验证结果
        assertNotNull(response, "上传响应不应为空");
        assertEquals("test.jpg", response.getOriginalName(), "原始文件名应该正确");
        assertNotNull(response.getStoredName(), "存储文件名不应为空");
        assertEquals(testFile.getSize(), response.getFileSize(), "文件大小应该正确");
        // 验证数据库保存方法被调用了一次
        verify(fileInfoRepository, times(1)).save(any(FileInfo.class));
    }

    /**
     * 测试上传重复文件的场景
     *
     * 测试条件：
     * - 上传的文件已存在（通过MD5哈希值判断）
     * - 系统应该返回已存在文件的信息，而不是重新保存
     *
     * 验证点：
     * - 返回的响应对象不为空
     * - 返回已存在文件的信息
     * - 不会执行数据库保存操作（避免重复存储）
     */
    @Test
    void testUploadFile_DuplicateFile() throws IOException {
        // Given - 准备测试数据，模拟已存在的文件
        FileInfo existingFile = new FileInfo();
        existingFile.setId(1L);
        existingFile.setOriginalName("test.jpg");
        existingFile.setStoredName("stored_test.jpg");
        existingFile.setFileSize((long) testFile.getSize());

        // 模拟数据库中存在相同MD5哈希值的文件
        when(fileInfoRepository.findByMd5HashAndIsDeletedFalse(any())).thenReturn(Optional.of(existingFile));

        // When - 执行被测试的方法
        FileUploadResponse response = fileService.uploadFile(testFile, "127.0.0.1");

        // Then - 验证结果
        assertNotNull(response, "上传响应不应为空");
        assertEquals("test.jpg", response.getOriginalName(), "应该返回原始文件名");
        assertEquals("stored_test.jpg", response.getStoredName(), "应该返回已存在文件的存储名");
        // 验证不会执行保存操作，因为文件已存在
        verify(fileInfoRepository, never()).save(any(FileInfo.class));
    }

    /**
     * 测试文件存在性检查 - 文件存在的情况
     *
     * 测试条件：
     * - 数据库中存在指定存储名的文件记录
     *
     * 验证点：
     * - 方法应返回true，表示文件存在
     */
    @Test
    void testFileExists_True() {
        // Given - 准备测试数据
        FileInfo fileInfo = new FileInfo();
        fileInfo.setStoredName("test.jpg");
        // 模拟数据库中存在该文件记录
        when(fileInfoRepository.findByStoredNameAndIsDeletedFalse("test.jpg")).thenReturn(Optional.of(fileInfo));

        // When - 执行被测试的方法
        boolean exists = fileService.fileExists("test.jpg");

        // Then - 验证结果
        assertTrue(exists, "当文件存在时应返回true");
    }

    /**
     * 测试文件存在性检查 - 文件不存在的情况
     *
     * 测试条件：
     * - 数据库中不存在指定存储名的文件记录
     *
     * 验证点：
     * - 方法应返回false，表示文件不存在
     */
    @Test
    void testFileExists_False() {
        // Given - 准备测试数据
        // 模拟数据库中不存在该文件记录
        when(fileInfoRepository.findByStoredNameAndIsDeletedFalse("test.jpg")).thenReturn(Optional.empty());

        // When - 执行被测试的方法
        boolean exists = fileService.fileExists("test.jpg");

        // Then - 验证结果
        assertFalse(exists, "当文件不存在时应返回false");
    }

    /**
     * 测试文件删除成功的场景
     *
     * 测试条件：
     * - 要删除的文件存在于数据库中
     * - 逻辑删除操作执行成功（返回影响行数大于0）
     *
     * 验证点：
     * - 方法应返回true，表示删除成功
     * - 逻辑删除方法被调用一次
     *
     * 注意：这里使用的是逻辑删除，即标记文件为已删除状态，
     * 而不是物理删除文件记录
     */
    @Test
    void testDeleteFile_Success() {
        // Given - 准备测试数据
        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(1L);
        fileInfo.setStoredName("test.jpg");
        fileInfo.setFilePath("/path/to/test.jpg");

        // 模拟数据库中存在该文件记录
        when(fileInfoRepository.findByStoredNameAndIsDeletedFalse("test.jpg")).thenReturn(Optional.of(fileInfo));
        // 模拟逻辑删除操作成功，返回影响行数为1
        when(fileInfoRepository.deleteFileLogically(1L)).thenReturn(1);

        // When - 执行被测试的方法
        boolean result = fileService.deleteFile("test.jpg");

        // Then - 验证结果
        assertTrue(result, "删除操作应该成功");
        // 验证逻辑删除方法被调用了一次
        verify(fileInfoRepository, times(1)).deleteFileLogically(1L);
    }
}