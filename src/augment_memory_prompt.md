- historySummaryMaxChars: 0 to 200000
  历史摘要最大字符数：0到200000字符
  - historySummaryLowerChars: 0 to 80000
    历史摘要较低字符数：0到80000字符
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
    历史摘要提示：空字符串到"您的任务是创建到目前为止对话的详细摘要，特别关注用户的明确请求和您之前的操作。
This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.
此摘要应该全面捕获技术细节、代码模式和架构决策，这些对于继续对话和支持任何持续任务都是必不可少的。

Your summary should be structured as follows:
您的摘要应按以下结构组织：
Context: The context to continue the conversation with. If applicable based on the current task, this should include:
上下文：继续对话的上下文。如果基于当前任务适用，这应该包括：
1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.
1. 先前对话：关于与用户整个对话中讨论内容的高层次细节。这应该写得让人能够跟随一般的总体对话流程。
2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.
2. 当前工作：详细描述在此次总结对话请求之前正在进行的工作。特别注意对话中较近期的消息。
3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.
3. 关键技术概念：列出所有重要的技术概念、技术、编码约定和框架，这些可能与继续此工作相关。
4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.
4. 相关文件和代码：如果适用，枚举为任务继续而检查、修改或创建的特定文件和代码部分。特别注意最近的消息和更改。
5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.
5. 问题解决：记录到目前为止解决的问题和任何正在进行的故障排除工作。
6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.
6. 待处理任务和下一步：概述您被明确要求处理的所有待处理任务，以及列出您将为所有未完成工作采取的下一步，如果适用的话。在有助于澄清的地方包含代码片段。对于任何下一步，包含最近对话中的直接引用，显示您正在处理的确切任务以及您离开的地方。这应该是逐字记录，以确保任务之间的上下文信息不会丢失。

Example summary structure:
示例摘要结构：
1. Previous Conversation:
1. 先前对话：
[Detailed description]
[详细描述]
2. Current Work:
2. 当前工作：
[Detailed description]
[详细描述]
3. Key Technical Concepts:
3. 关键技术概念：
- [Concept 1]
- [概念1]
- [Concept 2]
- [概念2]
- [...]
- [...]
4. Relevant Files and Code:
4. 相关文件和代码：
- [File Name 1]
- [文件名1]
    - [Summary of why this file is important]
    - [为什么这个文件重要的摘要]
    - [Summary of the changes made to this file, if any]
    - [对此文件所做更改的摘要，如果有的话]
    - [Important Code Snippet]
    - [重要代码片段]
- [File Name 2]
- [文件名2]
    - [Important Code Snippet]
    - [重要代码片段]
- [...]
- [...]
5. Problem Solving:
5. 问题解决：
[Detailed description]
[详细描述]
6. Pending Tasks and Next Steps:
6. 待处理任务和下一步：
- [Task 1 details & next steps]
- [任务1详情和下一步]
- [Task 2 details & next steps]
- [任务2详情和下一步]
- [...]
- [...]

Output only the summary of the conversation so far, without any additional commentary or explanation."
仅输出到目前为止对话的摘要，不要任何额外的评论或解释。"