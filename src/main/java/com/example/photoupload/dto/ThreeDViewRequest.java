package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * 3D视图请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "3D视图请求")
public class ThreeDViewRequest {

    @Schema(description = "文件名", example = "image.jpg")
    @JsonProperty("fileName")
    private String fileName;

    @Schema(description = "3D模型类型", example = "cube")
    @JsonProperty("modelType")
    private String modelType = "cube";

    @Schema(description = "深度强度", example = "0.5")
    @JsonProperty("depthIntensity")
    private float depthIntensity = 0.5f;

    @Schema(description = "纹理质量", example = "high")
    @JsonProperty("textureQuality")
    private String textureQuality = "medium";

    @Schema(description = "动画类型", example = "rotate")
    @JsonProperty("animationType")
    private String animationType = "rotate";

    @Schema(description = "动画速度", example = "1.0")
    @JsonProperty("animationSpeed")
    private float animationSpeed = 1.0f;

    @Schema(description = "背景颜色", example = "#000000")
    @JsonProperty("backgroundColor")
    private String backgroundColor = "#000000";

    @Schema(description = "光照设置")
    @JsonProperty("lighting")
    private LightingSettings lighting;

    @Schema(description = "相机设置")
    @JsonProperty("camera")
    private CameraSettings camera;

    @Schema(description = "额外参数")
    @JsonProperty("extraParams")
    private Map<String, Object> extraParams;

    // 构造函数
    public ThreeDViewRequest() {
        this.lighting = new LightingSettings();
        this.camera = new CameraSettings();
    }

    // Getters and Setters
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public float getDepthIntensity() {
        return depthIntensity;
    }

    public void setDepthIntensity(float depthIntensity) {
        this.depthIntensity = depthIntensity;
    }

    public String getTextureQuality() {
        return textureQuality;
    }

    public void setTextureQuality(String textureQuality) {
        this.textureQuality = textureQuality;
    }

    public String getAnimationType() {
        return animationType;
    }

    public void setAnimationType(String animationType) {
        this.animationType = animationType;
    }

    public float getAnimationSpeed() {
        return animationSpeed;
    }

    public void setAnimationSpeed(float animationSpeed) {
        this.animationSpeed = animationSpeed;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public LightingSettings getLighting() {
        return lighting;
    }

    public void setLighting(LightingSettings lighting) {
        this.lighting = lighting;
    }

    public CameraSettings getCamera() {
        return camera;
    }

    public void setCamera(CameraSettings camera) {
        this.camera = camera;
    }

    public Map<String, Object> getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(Map<String, Object> extraParams) {
        this.extraParams = extraParams;
    }

    /**
     * 光照设置内部类
     */
    @Schema(description = "光照设置")
    public static class LightingSettings {
        @Schema(description = "环境光强度", example = "0.3")
        private float ambientIntensity = 0.3f;

        @Schema(description = "方向光强度", example = "0.7")
        private float directionalIntensity = 0.7f;

        @Schema(description = "光源位置X", example = "1.0")
        private float lightPositionX = 1.0f;

        @Schema(description = "光源位置Y", example = "1.0")
        private float lightPositionY = 1.0f;

        @Schema(description = "光源位置Z", example = "1.0")
        private float lightPositionZ = 1.0f;

        @Schema(description = "光源颜色", example = "#FFFFFF")
        private String lightColor = "#FFFFFF";

        // Getters and Setters
        public float getAmbientIntensity() { return ambientIntensity; }
        public void setAmbientIntensity(float ambientIntensity) { this.ambientIntensity = ambientIntensity; }

        public float getDirectionalIntensity() { return directionalIntensity; }
        public void setDirectionalIntensity(float directionalIntensity) { this.directionalIntensity = directionalIntensity; }

        public float getLightPositionX() { return lightPositionX; }
        public void setLightPositionX(float lightPositionX) { this.lightPositionX = lightPositionX; }

        public float getLightPositionY() { return lightPositionY; }
        public void setLightPositionY(float lightPositionY) { this.lightPositionY = lightPositionY; }

        public float getLightPositionZ() { return lightPositionZ; }
        public void setLightPositionZ(float lightPositionZ) { this.lightPositionZ = lightPositionZ; }

        public String getLightColor() { return lightColor; }
        public void setLightColor(String lightColor) { this.lightColor = lightColor; }
    }

    /**
     * 相机设置内部类
     */
    @Schema(description = "相机设置")
    public static class CameraSettings {
        @Schema(description = "相机位置X", example = "0.0")
        private float positionX = 0.0f;

        @Schema(description = "相机位置Y", example = "0.0")
        private float positionY = 0.0f;

        @Schema(description = "相机位置Z", example = "5.0")
        private float positionZ = 5.0f;

        @Schema(description = "视野角度", example = "45.0")
        private float fieldOfView = 45.0f;

        @Schema(description = "近裁剪面", example = "0.1")
        private float nearClip = 0.1f;

        @Schema(description = "远裁剪面", example = "100.0")
        private float farClip = 100.0f;

        // Getters and Setters
        public float getPositionX() { return positionX; }
        public void setPositionX(float positionX) { this.positionX = positionX; }

        public float getPositionY() { return positionY; }
        public void setPositionY(float positionY) { this.positionY = positionY; }

        public float getPositionZ() { return positionZ; }
        public void setPositionZ(float positionZ) { this.positionZ = positionZ; }

        public float getFieldOfView() { return fieldOfView; }
        public void setFieldOfView(float fieldOfView) { this.fieldOfView = fieldOfView; }

        public float getNearClip() { return nearClip; }
        public void setNearClip(float nearClip) { this.nearClip = nearClip; }

        public float getFarClip() { return farClip; }
        public void setFarClip(float farClip) { this.farClip = farClip; }
    }
}
