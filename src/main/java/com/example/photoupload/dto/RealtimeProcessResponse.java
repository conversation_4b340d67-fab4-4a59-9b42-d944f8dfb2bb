package com.example.photoupload.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 实时处理响应DTO
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "实时处理响应")
public class RealtimeProcessResponse {

    @Schema(description = "会话ID", example = "session_123456")
    @JsonProperty("sessionId")
    private String sessionId;

    @Schema(description = "请求ID", example = "req_123456")
    @JsonProperty("requestId")
    private String requestId;

    @Schema(description = "处理状态", example = "success")
    @JsonProperty("status")
    private String status;

    @Schema(description = "响应消息", example = "处理成功")
    @JsonProperty("message")
    private String message;

    @Schema(description = "处理后的图像Base64数据")
    @JsonProperty("processedImageData")
    private String processedImageData;

    @Schema(description = "预览图像Base64数据")
    @JsonProperty("previewImageData")
    private String previewImageData;

    @Schema(description = "处理进度", example = "85")
    @JsonProperty("progress")
    private Integer progress;

    @Schema(description = "处理时间(毫秒)", example = "1500")
    @JsonProperty("processingTime")
    private Long processingTime;

    @Schema(description = "处理开始时间")
    @JsonProperty("startTime")
    private LocalDateTime startTime;

    @Schema(description = "处理结束时间")
    @JsonProperty("endTime")
    private LocalDateTime endTime;

    @Schema(description = "图像信息")
    @JsonProperty("imageInfo")
    private ImageInfo imageInfo;

    @Schema(description = "错误详情")
    @JsonProperty("errorDetails")
    private String errorDetails;

    @Schema(description = "额外数据")
    @JsonProperty("extraData")
    private Map<String, Object> extraData;

    // 构造函数
    public RealtimeProcessResponse() {
        this.startTime = LocalDateTime.now();
    }

    // 便捷方法
    public static RealtimeProcessResponse success(String sessionId, String requestId, String message) {
        RealtimeProcessResponse response = new RealtimeProcessResponse();
        response.setSessionId(sessionId);
        response.setRequestId(requestId);
        response.setStatus("success");
        response.setMessage(message);
        response.setProgress(100);
        response.setEndTime(LocalDateTime.now());
        return response;
    }

    public static RealtimeProcessResponse processing(String sessionId, String requestId, String message, int progress) {
        RealtimeProcessResponse response = new RealtimeProcessResponse();
        response.setSessionId(sessionId);
        response.setRequestId(requestId);
        response.setStatus("processing");
        response.setMessage(message);
        response.setProgress(progress);
        return response;
    }

    public static RealtimeProcessResponse error(String sessionId, String requestId, String message, String errorDetails) {
        RealtimeProcessResponse response = new RealtimeProcessResponse();
        response.setSessionId(sessionId);
        response.setRequestId(requestId);
        response.setStatus("error");
        response.setMessage(message);
        response.setErrorDetails(errorDetails);
        response.setEndTime(LocalDateTime.now());
        return response;
    }

    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getProcessedImageData() {
        return processedImageData;
    }

    public void setProcessedImageData(String processedImageData) {
        this.processedImageData = processedImageData;
    }

    public String getPreviewImageData() {
        return previewImageData;
    }

    public void setPreviewImageData(String previewImageData) {
        this.previewImageData = previewImageData;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public ImageInfo getImageInfo() {
        return imageInfo;
    }

    public void setImageInfo(ImageInfo imageInfo) {
        this.imageInfo = imageInfo;
    }

    public String getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }

    public Map<String, Object> getExtraData() {
        return extraData;
    }

    public void setExtraData(Map<String, Object> extraData) {
        this.extraData = extraData;
    }

    /**
     * 图像信息内部类
     */
    @Schema(description = "图像信息")
    public static class ImageInfo {
        @Schema(description = "图像宽度", example = "1920")
        private int width;

        @Schema(description = "图像高度", example = "1080")
        private int height;

        @Schema(description = "图像格式", example = "JPEG")
        private String format;

        @Schema(description = "文件大小(字节)", example = "1048576")
        private long fileSize;

        // 构造函数
        public ImageInfo() {}

        public ImageInfo(int width, int height, String format, long fileSize) {
            this.width = width;
            this.height = height;
            this.format = format;
            this.fileSize = fileSize;
        }

        // Getters and Setters
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }

        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }

        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }

        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
    }
}
