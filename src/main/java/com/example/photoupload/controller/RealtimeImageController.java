package com.example.photoupload.controller;

import com.example.photoupload.dto.RealtimeProcessRequest;
import com.example.photoupload.dto.RealtimeProcessResponse;
import com.example.photoupload.service.RealtimeImageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

/**
 * 实时图像处理控制器
 * 支持WebSocket实时图像处理功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
@Tag(name = "实时图像处理", description = "WebSocket实时图像处理API")
public class RealtimeImageController {

    private static final Logger logger = LoggerFactory.getLogger(RealtimeImageController.class);

    @Autowired
    private RealtimeImageService realtimeImageService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    /**
     * 处理实时滤镜预览请求
     */
    @MessageMapping("/filter/preview")
    @SendTo("/topic/filter/result")
    public RealtimeProcessResponse previewFilter(@Payload RealtimeProcessRequest request,
                                               SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        logger.info("收到实时滤镜预览请求: sessionId={}, filter={}", sessionId, request.getFilterType());

        try {
            // 异步处理滤镜预览
            CompletableFuture<RealtimeProcessResponse> future = 
                realtimeImageService.previewFilter(request, sessionId);
            
            // 立即返回处理中状态
            RealtimeProcessResponse response = new RealtimeProcessResponse();
            response.setSessionId(sessionId);
            response.setRequestId(request.getRequestId());
            response.setStatus("processing");
            response.setMessage("正在处理滤镜预览...");
            
            // 异步完成后发送结果
            future.thenAccept(result -> {
                messagingTemplate.convertAndSendToUser(
                    sessionId, "/queue/filter/result", result);
            }).exceptionally(throwable -> {
                logger.error("滤镜预览处理失败", throwable);
                RealtimeProcessResponse errorResponse = new RealtimeProcessResponse();
                errorResponse.setSessionId(sessionId);
                errorResponse.setRequestId(request.getRequestId());
                errorResponse.setStatus("error");
                errorResponse.setMessage("滤镜预览处理失败: " + throwable.getMessage());
                
                messagingTemplate.convertAndSendToUser(
                    sessionId, "/queue/filter/result", errorResponse);
                return null;
            });
            
            return response;
            
        } catch (Exception e) {
            logger.error("处理实时滤镜预览请求失败", e);
            RealtimeProcessResponse errorResponse = new RealtimeProcessResponse();
            errorResponse.setSessionId(sessionId);
            errorResponse.setRequestId(request.getRequestId());
            errorResponse.setStatus("error");
            errorResponse.setMessage("处理失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 处理实时裁剪预览请求
     */
    @MessageMapping("/crop/preview")
    @SendTo("/topic/crop/result")
    public RealtimeProcessResponse previewCrop(@Payload RealtimeProcessRequest request,
                                             SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        logger.info("收到实时裁剪预览请求: sessionId={}", sessionId);

        try {
            CompletableFuture<RealtimeProcessResponse> future = 
                realtimeImageService.previewCrop(request, sessionId);
            
            RealtimeProcessResponse response = new RealtimeProcessResponse();
            response.setSessionId(sessionId);
            response.setRequestId(request.getRequestId());
            response.setStatus("processing");
            response.setMessage("正在处理裁剪预览...");
            
            future.thenAccept(result -> {
                messagingTemplate.convertAndSendToUser(
                    sessionId, "/queue/crop/result", result);
            }).exceptionally(throwable -> {
                logger.error("裁剪预览处理失败", throwable);
                RealtimeProcessResponse errorResponse = new RealtimeProcessResponse();
                errorResponse.setSessionId(sessionId);
                errorResponse.setRequestId(request.getRequestId());
                errorResponse.setStatus("error");
                errorResponse.setMessage("裁剪预览处理失败: " + throwable.getMessage());
                
                messagingTemplate.convertAndSendToUser(
                    sessionId, "/queue/crop/result", errorResponse);
                return null;
            });
            
            return response;
            
        } catch (Exception e) {
            logger.error("处理实时裁剪预览请求失败", e);
            RealtimeProcessResponse errorResponse = new RealtimeProcessResponse();
            errorResponse.setSessionId(sessionId);
            errorResponse.setRequestId(request.getRequestId());
            errorResponse.setStatus("error");
            errorResponse.setMessage("处理失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 处理实时调色预览请求
     */
    @MessageMapping("/adjust/preview")
    @SendTo("/topic/adjust/result")
    public RealtimeProcessResponse previewAdjustment(@Payload RealtimeProcessRequest request,
                                                   SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        logger.info("收到实时调色预览请求: sessionId={}", sessionId);

        try {
            CompletableFuture<RealtimeProcessResponse> future = 
                realtimeImageService.previewAdjustment(request, sessionId);
            
            RealtimeProcessResponse response = new RealtimeProcessResponse();
            response.setSessionId(sessionId);
            response.setRequestId(request.getRequestId());
            response.setStatus("processing");
            response.setMessage("正在处理调色预览...");
            
            future.thenAccept(result -> {
                messagingTemplate.convertAndSendToUser(
                    sessionId, "/queue/adjust/result", result);
            }).exceptionally(throwable -> {
                logger.error("调色预览处理失败", throwable);
                RealtimeProcessResponse errorResponse = new RealtimeProcessResponse();
                errorResponse.setSessionId(sessionId);
                errorResponse.setRequestId(request.getRequestId());
                errorResponse.setStatus("error");
                errorResponse.setMessage("调色预览处理失败: " + throwable.getMessage());
                
                messagingTemplate.convertAndSendToUser(
                    sessionId, "/queue/adjust/result", errorResponse);
                return null;
            });
            
            return response;
            
        } catch (Exception e) {
            logger.error("处理实时调色预览请求失败", e);
            RealtimeProcessResponse errorResponse = new RealtimeProcessResponse();
            errorResponse.setSessionId(sessionId);
            errorResponse.setRequestId(request.getRequestId());
            errorResponse.setStatus("error");
            errorResponse.setMessage("处理失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取实时处理状态
     */
    @GetMapping("/api/realtime/status/{sessionId}")
    @ResponseBody
    @Operation(summary = "获取实时处理状态")
    public RealtimeProcessResponse getProcessingStatus(@PathVariable String sessionId) {
        return realtimeImageService.getProcessingStatus(sessionId);
    }

    /**
     * 取消实时处理
     */
    @PostMapping("/api/realtime/cancel/{sessionId}")
    @ResponseBody
    @Operation(summary = "取消实时处理")
    public RealtimeProcessResponse cancelProcessing(@PathVariable String sessionId) {
        return realtimeImageService.cancelProcessing(sessionId);
    }
}
