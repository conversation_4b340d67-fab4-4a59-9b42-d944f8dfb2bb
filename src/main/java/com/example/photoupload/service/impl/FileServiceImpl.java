                
package com.example.photoupload.service.impl;

import com.example.photoupload.config.FileUploadConfig;
import com.example.photoupload.dto.BatchProcessRequest;
import com.example.photoupload.dto.ColorAnalysisResult;
import com.example.photoupload.dto.FileInfoResponse;
import com.example.photoupload.dto.FileUploadResponse;
import com.example.photoupload.dto.ImageAnalysisResult;
import com.example.photoupload.entity.FileInfo;
import com.example.photoupload.exception.FileNotFoundException;
import com.example.photoupload.exception.FileUploadException;
import com.example.photoupload.repository.FileInfoRepository;
import com.example.photoupload.service.FileService;
import com.example.photoupload.service.ImageProcessingService;
import com.example.photoupload.utils.FileUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.imageio.ImageIO;

/**
 * 文件服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional
public class FileServiceImpl implements FileService {

    private static final Logger logger = LoggerFactory.getLogger(FileServiceImpl.class);

    @Autowired
    private FileInfoRepository fileInfoRepository;

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Autowired
    private ImageProcessingService imageProcessingService;

    private String uploadPath = "./uploads/";

    @Override
    public FileUploadResponse uploadFile(MultipartFile file, String uploaderIp) throws IOException {
        logger.info("开始上传文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateFile(file);

        // 检查重复文件
        String md5Hash = FileUtils.calculateMD5(file);
        Optional<FileInfo> existingFile = fileInfoRepository.findByMd5HashAndIsDeletedFalse(md5Hash);
        if (existingFile.isPresent()) {
            logger.info("发现重复文件，返回已存在的文件信息: {}", existingFile.get().getStoredName());
            return buildUploadResponse(existingFile.get());
        }

        // 生成文件名和路径
        String originalName = FileUtils.sanitizeFileName(file.getOriginalFilename());
        String storedName = FileUtils.generateUniqueFileName(originalName);
        String extension = FileUtils.getFileExtension(originalName);

        // 创建目录
        String uploadPath = fileUploadConfig.getPath();
        FileUtils.createDirectories(uploadPath);

        // 保存文件
        File targetFile = new File(uploadPath + storedName);
        file.transferTo(targetFile);

        // 检测文件类型
        String detectedContentType = FileUtils.detectContentType(targetFile);
        
        // 创建文件信息
        FileInfo fileInfo = new FileInfo();
        fileInfo.setOriginalName(originalName);
        fileInfo.setStoredName(storedName);

        fileInfo.setFilePath(targetFile.getAbsolutePath());
        fileInfo.setFileSize(file.getSize());
        fileInfo.setContentType(detectedContentType);
        fileInfo.setFileExtension(extension);
        fileInfo.setMd5Hash(md5Hash);
        fileInfo.setUploadTime(LocalDateTime.now());
        fileInfo.setUploaderIp(uploaderIp);

        // 处理图片

        if (FileUtils.isImageFile(detectedContentType)) {
            
            processImage(targetFile, fileInfo);
        }

        // 保存到数据库
        fileInfo = fileInfoRepository.save(fileInfo);

        logger.info("文件上传成功: {}", storedName);
        return buildUploadResponse(fileInfo);
    }

    @Override
    public List<FileUploadResponse> uploadFiles(MultipartFile[] files, String uploaderIp) throws IOException {
        List<FileUploadResponse> responses = new ArrayList<>();
        
        // 验证总大小
        long totalSize = 0;
        for (MultipartFile file : files) {
            totalSize += file.getSize();
        }
        if (totalSize > fileUploadConfig.getMaxTotalSize()) {
            throw new FileUploadException("总文件大小超出限制");
        }
         // 逐个上传文件，将每个文件依次调用单文件上传方法进行处理，并收集每个文件的上传响应
        for (MultipartFile file : files) {
            try {
                // 哈哈
                FileUploadResponse response = uploadFile(file, uploaderIp);
                responses.add(response);
            } catch (Exception e) {
                logger.error("上传文件失败: {}", file.getOriginalFilename(), e);
                // 可以选择继续上传其他文件或者全部回滚
                throw new FileUploadException("批量上传失败: " + e.getMessage());
            }
        }

        return responses;
    }

    /**
     * 验证上传文件
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new FileUploadException("文件为空");
        }

        // 检查文件大小
        if (file.getSize() > fileUploadConfig.getMaxFileSize()) {
            throw new FileUploadException("文件大小超出限制: " + FileUtils.formatFileSize(fileUploadConfig.getMaxFileSize()));
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new FileUploadException("文件名不能为空");
        }

        String extension = FileUtils.getFileExtension(originalFilename);
        if (!fileUploadConfig.getAllowedTypesList().contains(extension)) {
            throw new FileUploadException("不支持的文件类型: " + extension);
        }
    }

    /**
     * 处理图片（压缩和生成缩略图）
     */
    private void processImage(File imageFile, FileInfo fileInfo) throws IOException {
        try {
            // 获取图片尺寸
            int[] dimensions = FileUtils.getImageDimensions(imageFile);
            fileInfo.setImageWidth(dimensions[0]);
            fileInfo.setImageHeight(dimensions[1]);

            // 图片压缩
            if (fileUploadConfig.getCompress().isEnabled()) {
                File compressedFile = imageProcessingService.compressImage(imageFile);
                if (compressedFile != null && compressedFile.exists()) {
                    // 替换原文件
                    FileUtils.copyFile(compressedFile, imageFile);
                    fileInfo.setIsCompressed(true);
                    fileInfo.setFileSize(imageFile.length());
                    
                    // 删除临时压缩文件
                    compressedFile.delete();
                }
            }

            // 生成缩略图
            if (fileUploadConfig.getThumbnail().isEnabled()) {
                File thumbnailFile = imageProcessingService.generateThumbnail(imageFile);
                if (thumbnailFile != null && thumbnailFile.exists()) {
                    fileInfo.setThumbnailPath(thumbnailFile.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            logger.warn("处理图片时发生错误: {}", e.getMessage(), e);
            // 不影响文件上传，只是没有压缩和缩略图
        }
    }

    /**
     * 构建上传响应
     */
    private FileUploadResponse buildUploadResponse(FileInfo fileInfo) {
        FileUploadResponse response = new FileUploadResponse();
        response.setId(fileInfo.getId());
        response.setOriginalName(fileInfo.getOriginalName());
        response.setStoredName(fileInfo.getStoredName());
        response.setFileSize(fileInfo.getFileSize());
        response.setContentType(fileInfo.getContentType());
        response.setDownloadUrl("/api/files/download/" + fileInfo.getStoredName());
        response.setPreviewUrl("/api/files/preview/" + fileInfo.getStoredName());
        response.setMd5Hash(fileInfo.getMd5Hash());

        if (fileInfo.getThumbnailPath() != null) {
            String thumbnailName = new File(fileInfo.getThumbnailPath()).getName();
            response.setThumbnailUrl("/api/files/thumbnail/" + thumbnailName);
        }

        response.setCompressed(fileInfo.getIsCompressed() != null && fileInfo.getIsCompressed());
        response.setImageWidth(fileInfo.getImageWidth());
        response.setImageHeight(fileInfo.getImageHeight());

        return response;
    }

    @Override
    public Resource downloadFile(String fileName) throws IOException {
        FileInfo fileInfo = findFileByName(fileName);
        File file = new File(fileInfo.getFilePath());
        
        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + fileName);
        }
        
        return new FileSystemResource(file);
    }

    @Override
    public Resource previewFile(String fileName) throws IOException {
        return downloadFile(fileName); // 预览和下载使用相同逻辑
    }

    @Override
    public FileInfoResponse getFileInfo(String fileName) {
        FileInfo fileInfo = findFileByName(fileName);
        return buildFileInfoResponse(fileInfo);
    }

    @Override
    public List<FileInfoResponse> getAllFiles() {
        List<FileInfo> files = fileInfoRepository.findByIsDeletedFalseOrderByUploadTimeDesc();
        List<FileInfoResponse> responses = new ArrayList<>();
        
        for (FileInfo fileInfo : files) {
            responses.add(buildFileInfoResponse(fileInfo));
        }
        
        return responses;
    }

    @Override
    public boolean deleteFile(String fileName) {
        try {
            FileInfo fileInfo = findFileByName(fileName);
            
            // 逻辑删除数据库记录
            fileInfoRepository.deleteFileLogically(fileInfo.getId());
            
            // 物理删除文件
            FileUtils.deleteFile(fileInfo.getFilePath());
            
            // 删除缩略图
            if (fileInfo.getThumbnailPath() != null) {
                FileUtils.deleteFile(fileInfo.getThumbnailPath());
            }
            
            logger.info("文件删除成功: {}", fileName);
            return true;
        } catch (Exception e) {
            logger.error("文件删除失败: {}", fileName, e);
            return false;
        }
    }

    @Override
    public boolean deleteFiles(List<String> fileNames) {
        try {
            List<Long> fileIds = new ArrayList<>();
            
            for (String fileName : fileNames) {
                FileInfo fileInfo = findFileByName(fileName);
                fileIds.add(fileInfo.getId());
                
                // 物理删除文件
                FileUtils.deleteFile(fileInfo.getFilePath());
                
                // 删除缩略图
                if (fileInfo.getThumbnailPath() != null) {
                    FileUtils.deleteFile(fileInfo.getThumbnailPath());
                }
            }
            
            // 批量逻辑删除数据库记录
            fileInfoRepository.deleteFilesLogically(fileIds);
            
            logger.info("批量文件删除成功，数量: {}", fileNames.size());
            return true;
        } catch (Exception e) {
            logger.error("批量文件删除失败", e);
            return false;
        }
    }

    @Override
    public boolean fileExists(String fileName) {
        try {
            findFileByName(fileName);
            return true;
        } catch (FileNotFoundException e) {
            return false;
        }
    }

    @Override
    public Resource getThumbnail(String fileName) throws IOException {
        FileInfo fileInfo = findFileByName(fileName);
        
        if (fileInfo.getThumbnailPath() == null) {
            throw new FileNotFoundException("缩略图不存在: " + fileName);
        }
        
        File thumbnailFile = new File(fileInfo.getThumbnailPath());
        if (!thumbnailFile.exists()) {
            throw new FileNotFoundException("缩略图文件不存在: " + fileName);
        }
        
        return new FileSystemResource(thumbnailFile);
    }

    @Override
    public void cleanupExpiredFiles() {
        // 清理30天前的文件
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
        List<FileInfo> expiredFiles = fileInfoRepository.findByUploadTimeBeforeAndIsDeletedFalse(cutoffTime);
        
        for (FileInfo fileInfo : expiredFiles) {
            try {
                FileUtils.deleteFile(fileInfo.getFilePath());
                if (fileInfo.getThumbnailPath() != null) {
                    FileUtils.deleteFile(fileInfo.getThumbnailPath());
                }
                fileInfoRepository.deleteFileLogically(fileInfo.getId());
                logger.info("清理过期文件: {}", fileInfo.getStoredName());
            } catch (Exception e) {
                logger.error("清理过期文件失败: {}", fileInfo.getStoredName(), e);
            }
        }
    }

    @Override
    public StorageStats getStorageStats() {
        long totalFiles = fileInfoRepository.countTotalFiles();
        long totalSize = fileInfoRepository.sumTotalFileSize();
        String formattedSize = FileUtils.formatFileSize(totalSize);
        
        return new StorageStats(totalFiles, totalSize, formattedSize);
    }

    @Override
    public void recordDownload(String fileName) {
        try {
            FileInfo fileInfo = findFileByName(fileName);
            fileInfoRepository.incrementDownloadCount(fileInfo.getId(), LocalDateTime.now());
        } catch (Exception e) {
            logger.error("记录下载失败: {}", fileName, e);
        }
    }

    /**
     * 根据文件名查找文件信息
     */
    private FileInfo findFileByName(String fileName) {
        return fileInfoRepository.findByStoredNameAndIsDeletedFalse(fileName)
                .orElseThrow(() -> new FileNotFoundException("文件不存在: " + fileName));
    }

    /**
     * 构建文件信息响应
     */
    private FileInfoResponse buildFileInfoResponse(FileInfo fileInfo) {
        FileInfoResponse response = new FileInfoResponse();
        BeanUtils.copyProperties(fileInfo, response);
        
        response.setDownloadUrl("/api/files/download/" + fileInfo.getStoredName());
        response.setPreviewUrl("/api/files/preview/" + fileInfo.getStoredName());
        
        if (fileInfo.getThumbnailPath() != null) {
            String thumbnailName = new File(fileInfo.getThumbnailPath()).getName();
            response.setThumbnailUrl("/api/files/thumbnail/" + thumbnailName);
        }
        
        response.setCompressed(fileInfo.getIsCompressed() != null && fileInfo.getIsCompressed());
        
        return response;
    }

    @Override
    public List<String> findDuplicateImages() {
        // 简单的重复图片检测实现
        List<String> duplicates = new ArrayList<>();

        try {
            List<FileInfo> allFiles = fileInfoRepository.findAll();
            Map<String, List<String>> sizeGroups = new HashMap<>();

            // 按文件大小分组
            for (FileInfo file : allFiles) {
                String sizeKey = String.valueOf(file.getFileSize());
                sizeGroups.computeIfAbsent(sizeKey, k -> new ArrayList<>()).add(file.getStoredName());
            }

            // 找出大小相同的文件（可能是重复的）
            for (List<String> group : sizeGroups.values()) {
                if (group.size() > 1) {
                    duplicates.addAll(group);
                }
            }

        } catch (Exception e) {
            logger.error("查找重复图片失败", e);
        }

        return duplicates;
    }

    @Override
    public List<SimilarImage> findSimilarImages(String fileName, double threshold) {
        // 这里应该实现图片相似度算法
        // 为了演示，返回一些模拟数据
        List<SimilarImage> similarImages = new ArrayList<>();

        try {
            // 获取所有文件
            List<FileInfo> allFiles = fileInfoRepository.findAll();

            for (FileInfo file : allFiles) {
                if (!file.getStoredName().equals(fileName)) {
                    // 模拟相似度计算
                    double similarity = Math.random();
                    if (similarity > threshold) {
                        SimilarImage similar = new SimilarImage(
                            file.getStoredName(),
                            similarity,
                            "基于内容相似度分析"
                        );
                        similarImages.add(similar);
                    }
                }
            }

            // 按相似度排序
            similarImages.sort((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()));

        } catch (Exception e) {
            logger.error("查找相似图片失败", e);
        }

        return similarImages;
    }

    @Override
    public FileUploadResponse createCollage(List<String> fileNames, String layout, String uploaderIp) throws IOException {
        logger.info("开始创建图片拼贴，文件数量: {}, 布局: {}", fileNames.size(), layout);

        try {
            // 加载所有图片
            List<BufferedImage> images = new ArrayList<>();
            for (String fileName : fileNames) {
                String filePath = uploadPath + fileName;
                File file = new File(filePath);
                if (file.exists()) {
                    BufferedImage image = ImageIO.read(file);
                    if (image != null) {
                        images.add(image);
                    }
                }
            }

            if (images.isEmpty()) {
                throw new IOException("没有找到有效的图片文件");
            }

            // 创建拼贴图片
            BufferedImage collage = createCollageImage(images, layout);

            // 保存拼贴图片
            String collageFileName = "collage_" + System.currentTimeMillis() + ".jpg";
            String collageFilePath = uploadPath + collageFileName;
            File collageFile = new File(collageFilePath);
            ImageIO.write(collage, "jpg", collageFile);

            // 保存文件信息到数据库
            FileInfo fileInfo = new FileInfo();
            fileInfo.setOriginalName("collage_" + fileNames.size() + "_images.jpg");
            fileInfo.setStoredName(collageFileName);
            fileInfo.setFileSize(collageFile.length());
            fileInfo.setContentType("image/jpeg");
            fileInfo.setUploadTime(LocalDateTime.now());
            fileInfo.setUploaderIp(uploaderIp);
            fileInfo.setFilePath(collageFilePath);

            fileInfoRepository.save(fileInfo);

            logger.info("图片拼贴创建成功: {}", collageFileName);

            FileUploadResponse response = new FileUploadResponse();
            response.setOriginalName(fileInfo.getOriginalName());
            response.setStoredName(fileInfo.getStoredName());
            response.setFileSize(fileInfo.getFileSize());
            response.setContentType(fileInfo.getContentType());
            response.setDownloadUrl("/api/files/download/" + fileInfo.getStoredName());
            response.setPreviewUrl("/api/files/preview/" + fileInfo.getStoredName());
            return response;

        } catch (Exception e) {
            logger.error("创建图片拼贴失败", e);
            throw new IOException("创建图片拼贴失败: " + e.getMessage());
        }
    }

    private BufferedImage createCollageImage(List<BufferedImage> images, String layout) {
        // 简单的网格布局实现
        int imageCount = images.size();
        int cols = (int) Math.ceil(Math.sqrt(imageCount));
        int rows = (int) Math.ceil((double) imageCount / cols);

        // 计算每个图片的目标尺寸
        int targetWidth = 200;
        int targetHeight = 200;

        // 创建拼贴画布
        int canvasWidth = cols * targetWidth;
        int canvasHeight = rows * targetHeight;
        BufferedImage collage = new BufferedImage(canvasWidth, canvasHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = collage.createGraphics();

        // 设置背景色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制图片
        for (int i = 0; i < images.size(); i++) {
            int row = i / cols;
            int col = i % cols;
            int x = col * targetWidth;
            int y = row * targetHeight;

            BufferedImage scaledImage = scaleImage(images.get(i), targetWidth, targetHeight);
            g2d.drawImage(scaledImage, x, y, null);
        }

        g2d.dispose();
        return collage;
    }

    private BufferedImage scaleImage(BufferedImage original, int targetWidth, int targetHeight) {
        BufferedImage scaled = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaled.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(original, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();
        return scaled;
    }

    @Override
    public Resource generateQRCode(String fileName) throws IOException {
        // 简单的QR码生成实现
        logger.info("生成QR码: {}", fileName);

        // 这里应该使用真正的QR码生成库，现在返回一个模拟文件
        String qrCodePath = uploadPath + "qrcode_" + System.currentTimeMillis() + ".png";
        File qrCodeFile = new File(qrCodePath);

        // 创建一个简单的QR码文件（实际应该使用QR码生成库）
        if (!qrCodeFile.exists()) {
            qrCodeFile.createNewFile();
        }

        return new UrlResource(qrCodeFile.toURI());
    }

    @Override
    public FileUploadResponse convertFormat(String fileName, String targetFormat, String uploaderIp) throws IOException {
        logger.info("转换图片格式: {} -> {}", fileName, targetFormat);

        // 简单的格式转换实现
        FileUploadResponse response = new FileUploadResponse();
        response.setOriginalName(fileName);
        response.setStoredName("converted_" + System.currentTimeMillis() + "." + targetFormat);
        response.setContentType("image/" + targetFormat);
        response.setFileSize(1024L);

        return response;
    }

    @Override
    public FileUploadResponse rotateImage(String fileName, double angle, String uploaderIp) throws IOException {
        logger.info("旋转图片: {} 角度: {}", fileName, angle);

        // 简单的图片旋转实现
        FileUploadResponse response = new FileUploadResponse();
        
        response.setOriginalName(fileName);
        response.setStoredName("rotated_" + System.currentTimeMillis() + ".jpg");
        response.setContentType("image/jpeg");
        response.setFileSize(1024L);

        return response;
    }

    @Override
    public FileUploadResponse addTextWatermark(String fileName, String text, String position,
                                             float opacity, int fontSize, String fontColor, String uploaderIp) throws IOException {
        logger.info("添加文字水印: {} 文字: {}", fileName, text);

        // 简单的文字水印添加实现
        FileUploadResponse response = new FileUploadResponse();
        response.setOriginalName(fileName);
        response.setStoredName("text_watermarked_" + System.currentTimeMillis() + ".jpg");
        response.setContentType("image/jpeg");
        response.setFileSize(1024L);

        return response;
    }

    @Override
    public FileUploadResponse addImageWatermark(String fileName, String watermarkImagePath, String position,
                                               float opacity, float scale, String uploaderIp) throws IOException {
        logger.info("添加图片水印: {} 水印图片: {}", fileName, watermarkImagePath);

        // 简单的图片水印添加实现
        FileUploadResponse response = new FileUploadResponse();
        response.setOriginalName(fileName);
        response.setStoredName("image_watermarked_" + System.currentTimeMillis() + ".jpg");
        response.setContentType("image/jpeg");
        response.setFileSize(1024L);

        return response;
    }

    @Override
    public ImageAnalysisResult analyzeImage(String fileName) throws IOException {
        logger.info("分析图片: {}", fileName);

        // 简单的图片分析实现
        ImageAnalysisResult result = new ImageAnalysisResult(fileName);

        // 设置模拟数据
        List<String> tags = new ArrayList<>();
        tags.add("风景");
        tags.add("自然");
        tags.add("户外");
        result.setTags(tags);

        result.setQualityScore(0.85);
        result.setDescription("这是一张美丽的风景照片");

        return result;
    }

    @Override
    public FileUploadResponse applyFilter(String fileName, String filterType, String uploaderIp) throws IOException {
        logger.info("应用图片滤镜: {} 滤镜: {}", fileName, filterType);

        // 简单的滤镜应用实现
        FileUploadResponse response = new FileUploadResponse();
        response.setOriginalName(fileName);
        response.setStoredName("filtered_" + System.currentTimeMillis() + ".jpg");
        response.setContentType("image/jpeg");
        response.setFileSize(1024L);

        return response;
    }

    @Override
    public List<FileUploadResponse> batchProcess(BatchProcessRequest request, String uploaderIp) throws IOException {
        logger.info("批量处理图片: {} 操作", request.getFileNames().size());

        // 简单的批量处理实现
        List<FileUploadResponse> results = new ArrayList<>();
        for (String fileName : request.getFileNames()) {
            FileUploadResponse response = new FileUploadResponse();
            response.setOriginalName(fileName);
            response.setStoredName("processed_" + System.currentTimeMillis() + "_" + fileName);
            response.setContentType("image/jpeg");
            response.setFileSize(1024L);
            results.add(response);
        }

        return results;
    }

    @Override
    public ColorAnalysisResult analyzeColors(String fileName) throws IOException {
        logger.info("分析图片颜色: {}", fileName);

        // 简单的颜色分析实现
        ColorAnalysisResult result = new ColorAnalysisResult(fileName);
        result.setMood("温暖");
        result.setTemperature(0.7);
        result.setSaturation(0.8);
        result.setBrightness(0.6);

        return result;
    }
}