package com.example.photoupload.controller;

import com.example.photoupload.dto.StyleTransferRequest;
import com.example.photoupload.dto.StyleTransferResponse;
import com.example.photoupload.service.StyleTransferService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 图像风格迁移控制器
 * 提供神经网络风格迁移功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/style-transfer")
@Tag(name = "图像风格迁移", description = "神经网络风格迁移API")
public class StyleTransferController {

    private static final Logger logger = LoggerFactory.getLogger(StyleTransferController.class);

    @Autowired
    private StyleTransferService styleTransferService;

    /**
     * 应用风格迁移
     */
    @PostMapping("/apply")
    @Operation(summary = "应用风格迁移", description = "将指定风格应用到图片上")
    public ResponseEntity<StyleTransferResponse> applyStyleTransfer(@RequestBody StyleTransferRequest request) {
        try {
            logger.info("应用风格迁移: contentImage={}, styleType={}", 
                       request.getContentImagePath(), request.getStyleType());
            
            StyleTransferResponse response = styleTransferService.applyStyleTransfer(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("风格迁移失败", e);
            StyleTransferResponse errorResponse = StyleTransferResponse.error("风格迁移失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 上传自定义风格图片
     */
    @PostMapping("/upload-style")
    @Operation(summary = "上传自定义风格", description = "上传自定义风格图片用于风格迁移")
    public ResponseEntity<StyleTransferResponse> uploadCustomStyle(
            @RequestParam("file") MultipartFile file,
            @RequestParam("styleName") String styleName,
            @RequestParam(value = "description", required = false) String description) {
        try {
            logger.info("上传自定义风格: styleName={}, fileSize={}", styleName, file.getSize());
            
            StyleTransferResponse response = styleTransferService.uploadCustomStyle(file, styleName, description);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("上传自定义风格失败", e);
            StyleTransferResponse errorResponse = StyleTransferResponse.error("上传失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取可用风格列表
     */
    @GetMapping("/styles")
    @Operation(summary = "获取风格列表", description = "获取所有可用的风格类型")
    public ResponseEntity<List<StyleTransferResponse.StyleInfo>> getAvailableStyles() {
        try {
            logger.info("获取可用风格列表");
            
            List<StyleTransferResponse.StyleInfo> styles = styleTransferService.getAvailableStyles();
            
            return ResponseEntity.ok(styles);
            
        } catch (Exception e) {
            logger.error("获取风格列表失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取风格迁移历史
     */
    @GetMapping("/history")
    @Operation(summary = "获取迁移历史", description = "获取用户的风格迁移历史记录")
    public ResponseEntity<List<StyleTransferResponse>> getTransferHistory() {
        try {
            logger.info("获取风格迁移历史");
            
            List<StyleTransferResponse> history = styleTransferService.getTransferHistory();
            
            return ResponseEntity.ok(history);
            
        } catch (Exception e) {
            logger.error("获取迁移历史失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 下载风格迁移结果
     */
    @GetMapping("/download/{transferId}")
    @Operation(summary = "下载迁移结果", description = "下载风格迁移后的图片")
    public ResponseEntity<Resource> downloadTransferResult(@PathVariable String transferId) {
        try {
            logger.info("下载风格迁移结果: transferId={}", transferId);
            
            Resource resource = styleTransferService.getTransferResult(transferId);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_JPEG)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + transferId + "_styled.jpg\"")
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("下载风格迁移结果失败: transferId={}", transferId, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 批量风格迁移
     */
    @PostMapping("/batch")
    @Operation(summary = "批量风格迁移", description = "对多张图片应用相同风格")
    public ResponseEntity<List<StyleTransferResponse>> batchStyleTransfer(@RequestBody List<StyleTransferRequest> requests) {
        try {
            logger.info("批量风格迁移: count={}", requests.size());
            
            List<StyleTransferResponse> responses = styleTransferService.batchStyleTransfer(requests);
            
            return ResponseEntity.ok(responses);
            
        } catch (Exception e) {
            logger.error("批量风格迁移失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 预览风格效果
     */
    @PostMapping("/preview")
    @Operation(summary = "预览风格效果", description = "快速预览风格迁移效果（低分辨率）")
    public ResponseEntity<StyleTransferResponse> previewStyleTransfer(@RequestBody StyleTransferRequest request) {
        try {
            logger.info("预览风格效果: contentImage={}, styleType={}", 
                       request.getContentImagePath(), request.getStyleType());
            
            StyleTransferResponse response = styleTransferService.previewStyleTransfer(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("预览风格效果失败", e);
            StyleTransferResponse errorResponse = StyleTransferResponse.error("预览失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取风格迁移进度
     */
    @GetMapping("/progress/{transferId}")
    @Operation(summary = "获取迁移进度", description = "获取风格迁移的处理进度")
    public ResponseEntity<StyleTransferResponse> getTransferProgress(@PathVariable String transferId) {
        try {
            logger.info("获取风格迁移进度: transferId={}", transferId);
            
            StyleTransferResponse response = styleTransferService.getTransferProgress(transferId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取迁移进度失败: transferId={}", transferId, e);
            StyleTransferResponse errorResponse = StyleTransferResponse.error("获取进度失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 取消风格迁移
     */
    @PostMapping("/cancel/{transferId}")
    @Operation(summary = "取消风格迁移", description = "取消正在进行的风格迁移任务")
    public ResponseEntity<StyleTransferResponse> cancelTransfer(@PathVariable String transferId) {
        try {
            logger.info("取消风格迁移: transferId={}", transferId);
            
            boolean cancelled = styleTransferService.cancelTransfer(transferId);
            
            StyleTransferResponse response = new StyleTransferResponse();
            response.setSuccess(cancelled);
            response.setMessage(cancelled ? "任务已取消" : "取消失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("取消风格迁移失败: transferId={}", transferId, e);
            StyleTransferResponse errorResponse = StyleTransferResponse.error("取消失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 删除风格迁移结果
     */
    @DeleteMapping("/{transferId}")
    @Operation(summary = "删除迁移结果", description = "删除风格迁移结果文件")
    public ResponseEntity<StyleTransferResponse> deleteTransferResult(@PathVariable String transferId) {
        try {
            logger.info("删除风格迁移结果: transferId={}", transferId);
            
            boolean deleted = styleTransferService.deleteTransferResult(transferId);
            
            StyleTransferResponse response = new StyleTransferResponse();
            response.setSuccess(deleted);
            response.setMessage(deleted ? "删除成功" : "删除失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除风格迁移结果失败: transferId={}", transferId, e);
            StyleTransferResponse errorResponse = StyleTransferResponse.error("删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 获取风格迁移统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取迁移统计", description = "获取风格迁移的统计信息")
    public ResponseEntity<StyleTransferResponse> getTransferStatistics() {
        try {
            logger.info("获取风格迁移统计");
            
            StyleTransferResponse response = styleTransferService.getTransferStatistics();
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取风格迁移统计失败", e);
            StyleTransferResponse errorResponse = StyleTransferResponse.error("获取统计失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 推荐风格
     */
    @GetMapping("/recommend")
    @Operation(summary = "推荐风格", description = "基于图片内容推荐合适的风格")
    public ResponseEntity<List<StyleTransferResponse.StyleInfo>> recommendStyles(@RequestParam String imagePath) {
        try {
            logger.info("推荐风格: imagePath={}", imagePath);
            
            List<StyleTransferResponse.StyleInfo> recommendations = styleTransferService.recommendStyles(imagePath);
            
            return ResponseEntity.ok(recommendations);
            
        } catch (Exception e) {
            logger.error("推荐风格失败: imagePath={}", imagePath, e);
            return ResponseEntity.badRequest().build();
        }
    }
}
